# PopCircle Development TODO

## Client Feedback Summary
> **Key Clarification**: Streaming/video calling only takes place in the **Creator Dashboard area**. Home and profile pages should only feature **recorded content**. The main focus is testing functionality with 2+ people for recorded video calling sessions that are post-processed and published to home/profile feeds.

---

## 🎯 CRITICAL PRIORITY - Phase 8 (Testing Ready)

### 1. Creator Dashboard Video Calling (CRITICAL)
- [x] **Fix Creator Dashboard Multi-Participant Setup**
  - [x] Ensure `/u/[username]` (Creator Dashboard) properly enables `enableMultiParticipant={true}`
  - [ ] Test 2+ person video calling sessions in Creator Dashboard
  - [ ] Verify PreJoin component works correctly for guests joining creator streams
  - [ ] Test room creation and participant management
  - [ ] Remove legacy stream player from `/u/[username]` page

### 2. Recording & Post-Processing Infrastructure (CRITICAL)
- [x] **Implement LiveKit Recording System**
  - [x] Set up LiveKit Room Composite Egress with active speaker layout
    - [x] Configure `RoomCompositeEgressRequest` with layout: "speaker"
    - [x] Use `EncodingOptionsPreset.H264_720P_30` for initial implementation
    - [x] Set up segmented file output for HLS streaming
    - [x] Configure file output to Cloudflare R2 bucket
  - [x] Create database schema for recorded sessions
  - [ ] Implement post-processing pipeline for recorded videos
  - [x] Set up Cloudflare R2 storage integration
    - [x] Configure R2 bucket for video storage
    - [ ] Implement multipart upload for large video files
    - [x] Set up presigned URLs for secure video access
  - [x] Create API endpoints for managing recorded content
    - [x] `/api/recording/start` - Start room composite egress
    - [x] `/api/recording/stop` - Stop egress and process video
    - [x] `/api/recording/status` - Check recording status
    - [x] `/api/videos/[id]` - Serve recorded videos from R2

- [x] **Database Schema Updates**
  - [x] Added RecordedSession model with all required fields
  - [x] Added RecordingStatus enum (PROCESSING, READY, FAILED, PUBLISHED)
  - [x] Added RecordedSessionParticipant model for tracking participants
  - [x] Updated User and Stream models with recording relations
  - [x] Applied schema changes to database

### 3. Home & Profile Page Recorded Content Display (CRITICAL)
- [x] **Remove Live Streaming from Public Pages**
  - [x] Update home page to show only recorded content (not live streams)
  - [ ] Update profile pages to show only recorded content
  - [x] Create recorded content feed components
  - [ ] Implement video player for recorded content

- [x] **Recorded Content Components**
  - [x] `RecordedContentLanding` component for home page display
  - [ ] `RecordedVideoPlayer` component for playback
  - [ ] `RecordedVideoGrid` component for profile pages
  - [ ] Pagination for recorded content feeds

---

## 🔧 MISSING LIVEKIT FUNCTIONALITY (HIGH PRIORITY)

### 4. Advanced LiveKit Features (Per Memory Analysis)
- [ ] **Update Dependencies**
  - [ ] Update `@livekit/components-react` from 2.9.0 to 2.9.9
  - [ ] Add `@livekit/track-processors: ^0.5.4` (virtual backgrounds)
  - [ ] Add `@livekit/krisp-noise-filter: 0.3.0` (noise cancellation)
  - [ ] Add `tinykeys: ^3.0.0` (already added, verify implementation)

- [ ] **Enhanced Settings Menu**
  - [ ] Add virtual background options to SettingsMenu
  - [ ] Add noise cancellation toggle
  - [ ] Add background blur option
  - [ ] Implement device selection improvements

- [ ] **Advanced Room Options & Custom Layouts**
  - [ ] Implement simulcast layers configuration
  - [ ] Add dynacast settings
  - [ ] Add adaptive streaming options
  - [ ] Add codec selection in room setup
  - [ ] Create custom layout templates for recording
    - [ ] Build custom speaker layout template (replace default "speaker")
    - [ ] Implement dynamic layout switching during recording
    - [ ] Add layout update API: `updateLayout(egressID, 'custom-speaker')`

- [ ] **Error Handling Improvements**
  - [ ] Add DeviceUnsupportedError handling
  - [ ] Add MediaDevicesError handling
  - [ ] Improve connection management
  - [ ] Add retry mechanisms for failed connections

---

## 🐛 CURRENT ISSUES TO FIX

### 5. Existing TODOs in Codebase
- [ ] **Block Service** (`actions/block.ts`)
  - [ ] Implement disconnect from livestream when blocking user
  - [ ] Add ability to kick guests from streams

### 6. Code Quality & Debugging
- [ ] **Remove Debug Code**
  - [ ] Remove console.log statements from production code
  - [ ] Clean up temporary debug components
  - [ ] Remove commented-out code sections

- [ ] **Error Handling**
  - [ ] Improve error messages for users
  - [ ] Add proper error boundaries
  - [ ] Implement graceful fallbacks for connection issues

---

## 🎨 UI/UX IMPROVEMENTS

### 7. Creator Dashboard UX
- [ ] **Pre-Join Experience**
  - [ ] Improve PreJoin component styling
  - [ ] Add device testing before joining
  - [ ] Add connection quality indicator
  - [ ] Implement better loading states

- [ ] **In-Session Experience**
  - [ ] Add participant management controls for hosts
  - [ ] Implement screen sharing controls
  - [ ] Add recording start/stop controls
  - [ ] Improve chat moderation tools

### 8. Recorded Content UX
- [ ] **Video Player Features**
  - [ ] Add video progress tracking
  - [ ] Implement video quality selection
  - [ ] Add playback speed controls
  - [ ] Add video sharing functionality

- [ ] **Content Discovery**
  - [ ] Add search functionality for recorded content
  - [ ] Implement content categories/tags
  - [ ] Add trending recorded content section
  - [ ] Implement content recommendations

---

## 🧪 TESTING REQUIREMENTS

### 10. Testing Infrastructure
- [ ] **Multi-User Testing**
  - [ ] Set up testing environment for 2+ users
  - [ ] Create test scenarios for Creator Dashboard sessions
  - [ ] Test recording and post-processing pipeline
  - [ ] Verify recorded content appears on home/profile feeds

- [ ] **Browser Compatibility**
  - [ ] Test on Chrome, Firefox, Safari, Edge
  - [ ] Test mobile browser compatibility
  - [ ] Test different screen sizes and resolutions

---

## 📱 MOBILE CONSIDERATIONS

### 11. Mobile Experience
- [ ] **Responsive Design**
  - [ ] Ensure Creator Dashboard works on mobile
  - [ ] Optimize video player for mobile
  - [ ] Test touch interactions for video controls
  - [ ] Implement mobile-specific UI patterns

---

## 🚀 DEPLOYMENT & MONITORING

### 12. Production Readiness
- [ ] **Environment Configuration**
  - [ ] Set up production LiveKit server with egress capabilities
  - [ ] Configure Cloudflare R2 bucket with proper CORS and access policies
  - [ ] Set up monitoring and logging for recording pipeline
  - [ ] Configure webhook endpoints for egress status updates

- [ ] **Analytics & Monitoring**
  - [ ] Add session analytics and recording metrics
  - [ ] Monitor R2 storage usage and costs
  - [ ] Set up error reporting and alerts for failed recordings

---

## 📋 IMMEDIATE NEXT STEPS (This Week)

1. **Test Current Creator Dashboard** - Verify 2+ person video calling works
2. ✅ **Implement LiveKit Room Composite Egress** 
   - ✅ Set up basic "speaker" layout recording to R2
   - ✅ Configure `livekit-server-sdk` for egress management
3. ✅ **Set up Cloudflare R2 Integration**
   - ✅ Create R2 bucket for video storage
   - ⏳ Implement multipart upload API for large video files
   - ✅ Set up presigned URLs for video access
4. ✅ **Implement Recording Database Schema** - Add RecordedSession models
5. ✅ **Create Recording API Endpoints** - Handle recording start/stop/processing
6. ✅ **Update Home/Profile Pages** - Remove live streams, add recorded content feeds
7. **Set up Test Environment** - Enable client testing with multiple users
8. **Add Recording Controls to Creator Dashboard** - ✅ Integrated recording controls
9. **Create Video Watch Page** - For playing recorded content
10. **Add Environment Variables** - Configure Cloudflare R2 credentials

---

## 🎯 SUCCESS CRITERIA

- [ ] 2+ people can join Creator Dashboard video calls
- [ ] Sessions are automatically recorded using LiveKit Room Composite Egress
- [ ] Recorded videos are stored in Cloudflare R2 with proper access controls
- [ ] Recorded content appears on home and profile feeds (replacing live streams)
- [ ] Client can successfully test the system with multiple participants
- [ ] No live streaming functionality on public pages (home/profile)
- [ ] All recorded content is properly post-processed and playable via R2

---

*Priority: Focus on Creator Dashboard functionality and recorded content display* 