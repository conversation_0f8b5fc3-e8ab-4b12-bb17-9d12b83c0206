"use server";

import { getSelf } from "@/lib/auth-service";
import { hasBlockedUser } from "@/lib/block-service";
import { getUserById } from "@/lib/user-service";
import { AccessToken } from "livekit-server-sdk";
import { v4 } from "uuid";
import { StreamRole } from "@prisma/client";

// Role-based permissions mapping
const getRolePermissions = (role: StreamRole, isHost: boolean) => {
  switch (role) {
    case StreamRole.HOST:
      return {
        canPublish: true,
        canPublishData: true,
        canPublishScreen: true,
        canSubscribe: true,
        canUpdateMetadata: true,
      };
    case StreamRole.CO_HOST:
      return {
        canPublish: true,
        canPublishData: true,
        canPublishScreen: true,
        canSubscribe: true,
        canUpdateMetadata: false,
      };
    case StreamRole.GUEST:
      return {
        canPublish: true,
        canPublishData: true,
        canPublishScreen: false,
        canSubscribe: true,
        canUpdateMetadata: false,
      };
    case StreamRole.VIEWER:
    default:
      return {
        canPublish: isHost, // Host can always publish, even as viewer
        canPublishData: true,
        canPublishScreen: false,
        canSubscribe: true,
        canUpdateMetadata: false,
      };
  }
};

export const createViewerToken = async (hostIdentity: string) => {
  let self;
  try {
    self = await getSelf();
    if (!self) {
      throw new Error("Not authenticated");
    }
  } catch {
    const id = v4();
    const username = `guest#${Math.floor(Math.random() * 1000)}`;
    self = { id, username };
  }
  const host = await getUserById(hostIdentity);

  if (!host) {
    throw new Error("User not found");
  }

  const isBlocked = await hasBlockedUser(host.id);

  if (isBlocked) {
    throw new Error("User is blocked");
  }

  const isHost = self.id === host.id;
  const role = isHost ? StreamRole.HOST : StreamRole.VIEWER;

  return createParticipantToken(hostIdentity, self.id, self.username, role);
};

export const createParticipantToken = async (
  hostIdentity: string,
  participantId: string,
  participantName: string,
  role: StreamRole = StreamRole.VIEWER
) => {
  const host = await getUserById(hostIdentity);

  if (!host) {
    throw new Error("Host not found");
  }

  const isHost = participantId === host.id;
  const permissions = getRolePermissions(role, isHost);

  const token = new AccessToken(
    process.env.LIVEKIT_API_KEY!,
    process.env.LIVEKIT_API_SECRET!,
    {
      identity: participantId, // Use consistent identity without prefixes
      name: participantName,
      metadata: JSON.stringify({ role, permissions, isHost }),
    }
  );

  token.addGrant({
    room: "global-dashboard-room",

    roomJoin: true,
    canPublish: permissions.canPublish,
    canPublishData: permissions.canPublishData,
    canSubscribe: permissions.canSubscribe,
    hidden: !isHost && role === StreamRole.VIEWER, // Hide anonymous viewers from participant list
  });

  return await Promise.resolve(token.toJwt());
};
