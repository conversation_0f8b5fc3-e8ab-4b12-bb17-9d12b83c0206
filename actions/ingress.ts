"use server";

import { getSelf } from "@/lib/auth-service";
import { db } from "@/lib/db";
import {
  type CreateIngressOptions,
  IngressInput,
  IngressVideoEncodingPreset,
  RoomServiceClient,
  TrackSource,
  IngressAudioEncodingPreset,
  IngressClient,
} from "livekit-server-sdk";
import { revalidatePath } from "next/cache";

const roomService = new RoomServiceClient(
  process.env.LIVEKIT_API_URL!,
  process.env.LIVEKIT_API_KEY!,
  process.env.LIVEKIT_API_SECRET!
);

const ingressClient = new IngressClient(
  process.env.LIVEKIT_API_URL!,
  process.env.LIVEKIT_API_KEY!,
  process.env.LIVEKIT_API_SECRET!
);

export const resetIngresses = async (hostIdentity: string) => {
  const ingresses = await ingressClient.listIngress({ roomName: hostIdentity });
  const rooms = await roomService.listRooms([hostIdentity]);

  for (const room of rooms) {
    await roomService.deleteRoom(room.name);
  }

  for (const ingress of ingresses) {
    if (ingress.ingressId) {
      await ingressClient.deleteIngress(ingress.ingressId);
    }
  }
};

export const createIngress = async (ingressType: string) => {
  const self = await getSelf();

  if (!self) {
    throw new Error("User not authenticated.");
  }

  await resetIngresses(self.id);

  const options: CreateIngressOptions = {
    name: self.username,
    roomName: self.id,
    participantName: self.username,
    participantIdentity: self.id,
  };

  if (ingressType === "WHIP_INPUT") {
    options.bypassTranscoding = true;
  }
  // Note: Video/audio encoding options removed for now due to SDK version compatibility
  // Will be re-added once we determine the correct API structure

  const ingress = await ingressClient.createIngress(
    IngressInput[ingressType as keyof typeof IngressInput],
    options
  );

  if (!ingress || !ingress.url || !ingress.streamKey) {
    throw new Error("Failed to create ingress");
  }

  // Find the user's primary stream (first one created)
  const userStream = await db.stream.findFirst({
    where: { userId: self.id },
    orderBy: { createdAt: 'asc' }
  });

  if (!userStream) {
    throw new Error("No stream found for user");
  }

  await db.stream.update({
    where: { id: userStream.id },
    data: {
      ingressId: ingress.ingressId,
      serverUrl: ingress.url,
      streamKey: ingress.streamKey,
    },
  });

  revalidatePath(`/u/${self.username}/keys`);
  return {
    ingressId: ingress.ingressId,
    serverUrl: ingress.url,
    streamKey: ingress.streamKey,
  }; // ✅ Return plain JSON object only
};
