"use server";

import { getSelf } from "@/lib/auth-service";
import { db } from "@/lib/db"; // ✅ Make sure this is imported
import { revalidatePath } from "next/cache";
import { Stream, StreamRole, ParticipantStatus } from "@prisma/client"; // ✅ Add this if needed
import {
  addParticipantToStream,
  removeParticipantFromStream,
  updateParticipantRole
} from "@/lib/stream-service";

export const updateStream = async (values: Partial<Stream>) => {
  try {
    const self = await getSelf();

    if (!self) {
      throw new Error("Unauthorized");
    }

    const selfStream = await db.stream.findFirst({
      where: { userId: self.id },
      orderBy: { createdAt: 'asc' }
    });

    if (!selfStream) {
      throw new Error("Stream not found");
    }

    const validData = {
      name: values.name,
      isChatEnabled: values.isChatEnabled,
      isChatFollowersOnly: values.isChatFollowersOnly,
      isChatDelayed: values.isChatDelayed,
    };

    const stream = await db.stream.update({
      where: { id: selfStream.id },
      data: { ...validData },
    });

    revalidatePath(`/u/${self.username}/chat`);
    revalidatePath(`/u/${self.username}`);
    revalidatePath(`/${self.username}`);

    return stream;
  } catch (error) {
    console.error("[UPDATE_STREAM_ERROR]", error);
    throw new Error("Internal Error");
  }
};

// Participant Management Actions
export const inviteParticipant = async (
  streamId: string,
  inviteeId: string,
  role: StreamRole = StreamRole.GUEST
) => {
  try {
    const self = await getSelf();

    if (!self) {
      throw new Error("Unauthorized");
    }

    // Check if user is host or co-host of the stream
    const stream = await db.stream.findUnique({
      where: { id: streamId },
      include: {
        user: true,
        participants: {
          where: {
            userId: self.id,
            status: ParticipantStatus.JOINED
          }
        }
      }
    });

    if (!stream) {
      throw new Error("Stream not found");
    }

    const isHost = stream.userId === self.id;
    const isCoHost = stream.participants.some(p =>
      p.userId === self.id && p.role === StreamRole.CO_HOST
    );

    if (!isHost && !isCoHost) {
      throw new Error("Only hosts and co-hosts can invite participants");
    }

    // Check if invitation already exists
    const existingInvitation = await db.streamInvitation.findUnique({
      where: {
        streamId_inviteeId: {
          streamId,
          inviteeId
        }
      }
    });

    if (existingInvitation && existingInvitation.status === ParticipantStatus.INVITED) {
      throw new Error("Invitation already sent");
    }

    // Create or update invitation
    const invitation = await db.streamInvitation.upsert({
      where: {
        streamId_inviteeId: {
          streamId,
          inviteeId
        }
      },
      update: {
        role,
        status: ParticipantStatus.INVITED,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        createdAt: new Date()
      },
      create: {
        streamId,
        inviterId: self.id,
        inviteeId,
        role,
        status: ParticipantStatus.INVITED,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      },
      include: {
        invitee: {
          select: {
            id: true,
            username: true,
            imageURL: true,
          }
        }
      }
    });

    revalidatePath(`/u/${stream.user.username}`);
    return invitation;
  } catch (error) {
    console.error("[INVITE_PARTICIPANT_ERROR]", error);
    throw new Error("Failed to invite participant");
  }
};

export const acceptInvitation = async (invitationId: string) => {
  try {
    const self = await getSelf();

    if (!self) {
      throw new Error("Unauthorized");
    }

    const invitation = await db.streamInvitation.findUnique({
      where: { id: invitationId },
      include: {
        stream: {
          include: {
            user: true
          }
        }
      }
    });

    if (!invitation) {
      throw new Error("Invitation not found");
    }

    if (invitation.inviteeId !== self.id) {
      throw new Error("Not authorized to accept this invitation");
    }

    if (invitation.status !== ParticipantStatus.INVITED) {
      throw new Error("Invitation is no longer valid");
    }

    if (invitation.expiresAt < new Date()) {
      throw new Error("Invitation has expired");
    }

    // Add participant to stream
    const participant = await addParticipantToStream(
      invitation.streamId,
      self.id,
      invitation.role
    );

    // Update invitation status
    await db.streamInvitation.update({
      where: { id: invitationId },
      data: { status: ParticipantStatus.JOINED }
    });

    revalidatePath(`/${invitation.stream.user.username}`);
    return participant;
  } catch (error) {
    console.error("[ACCEPT_INVITATION_ERROR]", error);
    throw new Error("Failed to accept invitation");
  }
};

export const kickParticipant = async (streamId: string, participantUserId: string) => {
  try {
    const self = await getSelf();

    if (!self) {
      throw new Error("Unauthorized");
    }

    // Check if user is host or co-host of the stream
    const stream = await db.stream.findUnique({
      where: { id: streamId },
      include: {
        user: true,
        participants: {
          where: {
            userId: self.id,
            status: ParticipantStatus.JOINED
          }
        }
      }
    });

    if (!stream) {
      throw new Error("Stream not found");
    }

    const isHost = stream.userId === self.id;
    const isCoHost = stream.participants.some(p =>
      p.userId === self.id && p.role === StreamRole.CO_HOST
    );

    if (!isHost && !isCoHost) {
      throw new Error("Only hosts and co-hosts can kick participants");
    }

    // Cannot kick the host
    if (participantUserId === stream.userId) {
      throw new Error("Cannot kick the host");
    }

    const result = await removeParticipantFromStream(streamId, participantUserId);

    revalidatePath(`/u/${stream.user.username}`);
    return result;
  } catch (error) {
    console.error("[KICK_PARTICIPANT_ERROR]", error);
    throw new Error("Failed to kick participant");
  }
};

export const changeParticipantRole = async (
  streamId: string,
  participantUserId: string,
  newRole: StreamRole
) => {
  try {
    const self = await getSelf();

    if (!self) {
      throw new Error("Unauthorized");
    }

    // Check if user is host of the stream
    const stream = await db.stream.findUnique({
      where: { id: streamId },
      include: {
        user: true
      }
    });

    if (!stream) {
      throw new Error("Stream not found");
    }

    const isHost = stream.userId === self.id;

    if (!isHost) {
      throw new Error("Only hosts can change participant roles");
    }

    // Cannot change host role
    if (participantUserId === stream.userId) {
      throw new Error("Cannot change host role");
    }

    const result = await updateParticipantRole(streamId, participantUserId, newRole);

    revalidatePath(`/u/${stream.user.username}`);
    return result;
  } catch (error) {
    console.error("[CHANGE_PARTICIPANT_ROLE_ERROR]", error);
    throw new Error("Failed to change participant role");
  }
};

// Stream sharing and link generation
export const getStreamShareLink = async (streamId?: string) => {
  try {
    const self = await getSelf();

    if (!self) {
      throw new Error("Unauthorized");
    }

    let stream;
    if (streamId) {
      // Get specific stream
      stream = await db.stream.findUnique({
        where: { id: streamId },
        include: { user: true }
      });
    } else {
      // Get user's primary stream
      stream = await db.stream.findFirst({
        where: { userId: self.id },
        include: { user: true },
        orderBy: { createdAt: 'asc' }
      });
    }

    if (!stream) {
      throw new Error("Stream not found");
    }

    // Only the host can get share links
    if (stream.userId !== self.id) {
      throw new Error("Only the stream host can generate share links");
    }

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    const shareLink = `${baseUrl}/${stream.user.username}`;

    return {
      shareLink,
      streamName: stream.name,
      hostUsername: stream.user.username,
      isLive: stream.isLive
    };
  } catch (error) {
    console.error("[GET_STREAM_SHARE_LINK_ERROR]", error);
    throw new Error("Failed to generate share link");
  }
};
