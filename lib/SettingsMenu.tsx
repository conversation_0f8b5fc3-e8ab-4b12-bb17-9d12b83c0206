"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CameraSettings } from "./CameraSettings";
import { MicrophoneSettings } from "./MicrophoneSettings";
import { useUser } from "@clerk/nextjs";
import { useRoomContext, useMaybeLayoutContext } from "@livekit/components-react";

export function SettingsMenu() {
  const [activeTab, setActiveTab] = useState<"media" | "recording">("media");
  const layoutContext = useMaybeLayoutContext();
  const { user } = useUser();
  const room = useRoomContext();

  const handleClose = () => {
    layoutContext?.widget.dispatch?.({ msg: 'toggle_settings' });
  };

  const handleStartRecording = async () => {
    if (!room || !user) return;

    try {
      const response = await fetch('/api/recording/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          roomName: room.name,
          title: `${user.username || 'Host'}'s Session`,
          description: `Recorded session from ${new Date().toLocaleDateString()}`,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start recording');
      }

      console.log('Recording started successfully');
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Settings</h2>
          <Button variant="ghost" size="sm" onClick={handleClose}>
            ×
          </Button>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b mb-4">
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === "media"
              ? "border-b-2 border-blue-500 text-blue-600"
              : "text-gray-500 hover:text-gray-700"
              }`}
            onClick={() => setActiveTab("media")}
          >
            Media
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === "recording"
              ? "border-b-2 border-blue-500 text-blue-600"
              : "text-gray-500 hover:text-gray-700"
              }`}
            onClick={() => setActiveTab("recording")}
          >
            Recording
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === "media" && (
          <div className="space-y-6">
            <CameraSettings />
            <MicrophoneSettings />
          </div>
        )}

        {activeTab === "recording" && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Recording Controls</h3>
            <p className="text-sm text-gray-600">
              Start recording to capture this session for later viewing.
            </p>
            <Button onClick={handleStartRecording} className="w-full">
              Start Recording
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
