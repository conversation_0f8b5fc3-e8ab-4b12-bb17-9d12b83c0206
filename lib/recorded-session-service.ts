import { db } from "@/lib/db";

export const getPublishedRecordedSessions = async () => {
    try {
        // First, let's debug all sessions to see what we have
        const allSessions = await db.recordedSession.findMany({
            include: {
                host: {
                    select: {
                        id: true,
                        username: true,
                        imageURL: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });

        console.log(`[DEBUG_RECORDED_SESSIONS] Total sessions in DB: ${allSessions.length}`);
        console.log('[DEBUG_RECORDED_SESSIONS] Session statuses:', allSessions.map(s => ({
            id: s.id,
            title: s.title,
            processingStatus: s.processingStatus,
            isPublished: s.isPublished,
            egressId: s.egressId,
            videoUrl: s.videoUrl,
            duration: s.duration,
            hostUsername: s.host.username,
            recordingStartedAt: s.recordingStartedAt,
            recordingEndedAt: s.recordingEndedAt
        })));

        const recordedSessions = await db.recordedSession.findMany({
            where: {
                isPublished: true,
                processingStatus: 'PUBLISHED',
            },
            include: {
                host: {
                    select: {
                        id: true,
                        username: true,
                        imageURL: true,
                    },
                },
            },
            orderBy: {
                publishedAt: 'desc',
            },
        });

        console.log(`[DEBUG_RECORDED_SESSIONS] Published sessions: ${recordedSessions.length}`);

        return recordedSessions;
    } catch (error) {
        console.error("Failed to get published recorded sessions:", error);
        return [];
    }
};

export const getRecordedSessionsByUser = async (userId: string) => {
    try {
        const recordedSessions = await db.recordedSession.findMany({
            where: {
                hostUserId: userId,
                isPublished: true,
                processingStatus: 'PUBLISHED',
            },
            include: {
                host: {
                    select: {
                        id: true,
                        username: true,
                        imageURL: true,
                    },
                },
            },
            orderBy: {
                publishedAt: 'desc',
            },
        });

        return recordedSessions;
    } catch (error) {
        console.error("Failed to get user's recorded sessions:", error);
        return [];
    }
};

export const getRecordedSessionById = async (sessionId: string) => {
    try {
        const recordedSession = await db.recordedSession.findUnique({
            where: {
                id: sessionId,
            },
            include: {
                host: {
                    select: {
                        id: true,
                        username: true,
                        imageURL: true,
                    },
                },
                participants: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                username: true,
                                imageURL: true,
                            },
                        },
                    },
                },
            },
        });

        return recordedSession;
    } catch (error) {
        console.error("Failed to get recorded session:", error);
        return null;
    }
}; 