import { db } from "@/lib/db";
import { getSelf } from "@/lib/auth-service";

export const getRecommended = async () => {
  await new Promise((resolve) => setTimeout(resolve, 500));
  let userId;
  try {
    const self = await getSelf();

    userId = self?.id || null;
  } catch {
    userId = null;
  }

  let users = []; // Ensure correct TypeScript type (if using TS)

  if (userId) {
    users = await db.user.findMany({
      where: {
        AND: [
          {
            NOT: {
              id: userId,
            },
          },
          { NOT: { followedBy: { some: { followerId: userId } } } },
          { NOT: { blocking: { some: { blockedId: userId } } } },
        ],
      },
      include: { streams: { select: { isLive: true }, take: 1 } },
      orderBy: {
        createdAt: "desc", // ✅ Fixed ordering syntax
      },
    });
  } else {
    users = await db.user.findMany({
      include: { streams: { select: { isLive: true }, take: 1 } },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  return users;
};
