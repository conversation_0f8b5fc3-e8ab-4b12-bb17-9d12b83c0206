import { db } from "@/lib/db";
import { currentUser } from "@clerk/nextjs/server";

export const getUserByUsername = async (username: string) => {
  // First, try finding the user by username
  let user = await db.user.findUnique({
    where: { username },
    include: {
      streams: {
        take: 1,
        orderBy: { createdAt: "desc" },
      },
    },
  });

  if (!user) {
    const clerkUser = await currentUser();

    if (clerkUser?.username && clerkUser.username === username) {
      // Check if user already exists by externalUserId
      const existing = await db.user.findUnique({
        where: { externalUserId: clerkUser.id },
      });

      if (existing) {
        user = await db.user.update({
          where: { externalUserId: clerkUser.id },
          data: { username: clerkUser.username }, // in case username changed
          include: {
            streams: {
              take: 1,
              orderBy: { createdAt: "desc" },
            },
          },
        });
      } else {
        user = await db.user.create({
          data: {
            externalUserId: clerkUser.id,
            username: clerkUser.username,
            imageURL: clerkUser.imageUrl || "",
            streams: {
              create: { name: `${clerkUser.username}'s stream` },
            },
          },
          include: {
            streams: {
              take: 1,
              orderBy: { createdAt: "desc" },
            },
          },
        });
      }
    }
  }

  return user;
};

export const getUserById = async (id: string) => {
  return db.user.findUnique({
    where: { id },
    include: {
      streams: {
        take: 1,
        orderBy: { createdAt: "desc" },
      },
    },
  });
};
