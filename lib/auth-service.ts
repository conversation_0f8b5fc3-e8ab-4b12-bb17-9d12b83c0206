import { currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";

export const getSelf = async () => {
  const self = await currentUser();

  // Not signed in or missing username → return null instead of throwing
  if (!self || !self.username) {
    return null;
  }

  let user = await db.user.findUnique({
    where: { externalUserId: self.id },
  });

  // User not found in DB → create them
  if (!user) {
    user = await db.user.create({
      data: {
        externalUserId: self.id,
        username: self.username,
        imageURL: self.imageUrl || "",
        streams: { create: { name: `${self.username}'s stream` } },
      },
    });
  }

  return user;
};

export const getSelfByUsername = async (username: string) => {
  const self = await currentUser();
  if (!self || !self.username) {
    throw new Error("Unauthorized");
  }

  let user = await db.user.findUnique({ where: { username } });

  // If user doesn't exist and this is the current user, create them
  if (!user && self.username === username) {
    user = await db.user.create({
      data: {
        externalUserId: self.id,
        username: self.username,
        imageURL: self.imageUrl || "",
        streams: { create: { name: `${username}'s stream` } },
      },
    });
  }

  if (!user) {
    throw new Error("User not found");
  }
  if (self.username !== user.username) {
    throw new Error("Unauthorized");
  }
  return user;
};
