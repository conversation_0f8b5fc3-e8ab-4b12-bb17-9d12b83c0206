import { db } from "@/lib/db";
import { getSelf } from "@/lib/auth-service";

// block-service.ts
// export const hasBlockedUser = async (id: string) => {
//   const self = await getSelf();
//   const block = await db.block.findUnique({
//     where: {
//       blockerId_blockedId: {
//         blockerId: self.id,
//         blockedId: self.id,
//       },
//     },
//   });

//   return !!block;
// };
export const hasBlockedUser = async (id: string) => {
  const self = await getSelf();
  // 👇 Handle unauthenticated users
  if (!self) {
    return false;
  }

  const existingBlock = await db.block.findUnique({
    where: {
      blockerId_blockedId: {
        blockerId: self.id, // ✅ YOU are the blocker
        blockedId: id, // ✅ They are the blocked
      },
    },
  });

  return !!existingBlock;
};

// export const hasBlockedUser = async (id: string) => {
//   const self = await getSelf();
//   const otherUser = await db.user.findUnique({ where: { id } });
//   if (!otherUser) {
//     throw new Error();
//   }
//   {
//     ("User not found");
//   }
//   if (otherUser.id === self.id) {
//     return false;
//   }
//   const existingBlock = await db.block.findUnique({
//     where: {
//       blockerId_blockedId: { blockerId: otherUser.id, blockedId: self.id },
//     },
//   });
// };

export const blockUser = async (id: string) => {
  const self = await getSelf();
  // 👇 Handle unauthenticated users
  if (!self) {
    return false;
  }
  if (self.id === id) {
    throw new Error("Cannot block yourself");
  }

  const otherUser = await db.user.findUnique({ where: { id } });
  if (!otherUser) {
    throw new Error("User not found");
  }

  const existingBlock = await db.block.findUnique({
    where: {
      blockerId_blockedId: {
        blockerId: self.id,
        blockedId: otherUser.id,
      },
    },
  });

  if (existingBlock) {
    throw new Error("Already blocked");
  }

  const block = await db.block.create({
    data: { blockerId: self.id, blockedId: otherUser.id },
    include: { blocked: true },
  });

  return block;
};

export const unblockUser = async (id: string) => {
  const self = await getSelf();
  // 👇 Handle unauthenticated users
  if (!self) {
    return false;
  }

  if (self.id === id) {
    throw new Error("Cannot unblock yourself");
  }

  const otherUser = await db.user.findUnique({ where: { id } });

  if (!otherUser) {
    throw new Error("User not found");
  }

  const existingBlock = await db.block.findUnique({
    where: {
      blockerId_blockedId: { blockerId: self.id, blockedId: otherUser.id },
    },
  });

  if (!existingBlock) {
    throw new Error("Not blocked");
  }

  const unblock = await db.block.delete({
    where: { id: existingBlock.id },
    include: { blocked: true },
  });

  return unblock;
};

export const getBlockedUsers = async () => {
  const self = await getSelf();
  // 👇 Handle unauthenticated users
  if (!self) {
    return [];
  }

  const blockedUsers = await db.block.findMany({
    where: {
      blockerId: self.id,
    },
    include: {
      blocked: {
        select: {
          id: true,
          username: true,
          imageURL: true,
        },
      },
    },
    orderBy: {
      id: "desc",
    },
  });

  return blockedUsers;
};
