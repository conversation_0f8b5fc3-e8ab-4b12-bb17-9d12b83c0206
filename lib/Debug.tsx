"use client";

import { useEffect } from "react";
import { LogLevel, setLogLevel } from "livekit-client";

interface DebugModeProps {
  logLevel?: LogLevel;
}

export function DebugMode({ logLevel = LogLevel.info }: DebugModeProps) {
  useEffect(() => {
    // Only enable debug logging in development
    if (process.env.NODE_ENV === 'development') {
      setLogLevel(logLevel);
      console.log(`[DEBUG] LiveKit log level set to: ${LogLevel[logLevel]}`);
    }
  }, [logLevel]);

  // This component doesn't render anything
  return null;
}
