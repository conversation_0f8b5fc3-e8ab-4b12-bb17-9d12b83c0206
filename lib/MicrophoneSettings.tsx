import React from 'react';
import { useKrispNoiseFilter } from '@livekit/components-react/krisp';
import { TrackToggle } from '@livekit/components-react';
import { MediaDeviceMenu } from '@livekit/components-react';
import { Track } from 'livekit-client';
import { But<PERSON> } from '@/components/ui/button';

export function MicrophoneSettings() {
    const { isNoiseFilterEnabled, setNoiseFilterEnabled, isNoiseFilterPending } = useKrispNoiseFilter();

    React.useEffect(() => {
        // enable Krisp by default
        setNoiseFilterEnabled(true);
    }, [setNoiseFilterEnabled]);

    return (
        <div className="flex flex-row gap-4 items-center justify-between">
            <div className="flex items-center space-x-2">
                <TrackToggle source={Track.Source.Microphone} />
                <span className="text-muted-foreground">Microphone</span>
                <div className="ml-auto">
                    <MediaDeviceMenu kind="audioinput" />
                </div>
            </div>

            <Button
                onClick={() => setNoiseFilterEnabled(!isNoiseFilterEnabled)}
                disabled={isNoiseFilterPending}
                variant={isNoiseFilterEnabled ? 'default' : 'outline'}
                size="sm"
            >
                {isNoiseFilterEnabled ? 'Disable' : 'Enable'} Enhanced Noise Cancellation
            </Button>
        </div>
    );
} 