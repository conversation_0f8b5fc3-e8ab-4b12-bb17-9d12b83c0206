import { db } from "@/lib/db";
import { StreamRole, ParticipantStatus } from "@prisma/client";

export const getStreamByUserId = async (userId: string) => {
  // Get the user's primary stream (first one created)
  const stream = await db.stream.findFirst({
    where: { userId },
    orderBy: { createdAt: 'asc' }
  });

  return stream;
};

// Multi-participant stream functions
export const getStreamParticipants = async (streamId: string) => {
  const participants = await db.streamParticipant.findMany({
    where: {
      streamId,
      status: ParticipantStatus.JOINED
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          imageURL: true,
        }
      }
    },
    orderBy: [
      { role: 'asc' }, // HOST first, then CO_HOST, GUEST, VIEWER
      { joinedAt: 'asc' }
    ]
  });

  return participants;
};

export const addParticipantToStream = async (
  streamId: string,
  userId: string,
  role: StreamRole = StreamRole.VIEWER
) => {
  // Check if participant already exists
  const existingParticipant = await db.streamParticipant.findUnique({
    where: {
      streamId_userId: {
        streamId,
        userId
      }
    }
  });

  if (existingParticipant) {
    // Update existing participant status to JOINED
    return await db.streamParticipant.update({
      where: { id: existingParticipant.id },
      data: {
        status: ParticipantStatus.JOINED,
        role,
        joinedAt: new Date(),
        leftAt: null
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            imageURL: true,
          }
        }
      }
    });
  }

  // Create new participant
  return await db.streamParticipant.create({
    data: {
      streamId,
      userId,
      role,
      status: ParticipantStatus.JOINED,
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          imageURL: true,
        }
      }
    }
  });
};

export const removeParticipantFromStream = async (streamId: string, userId: string) => {
  const participant = await db.streamParticipant.findUnique({
    where: {
      streamId_userId: {
        streamId,
        userId
      }
    }
  });

  if (!participant) {
    throw new Error("Participant not found");
  }

  return await db.streamParticipant.update({
    where: { id: participant.id },
    data: {
      status: ParticipantStatus.LEFT,
      leftAt: new Date()
    }
  });
};

export const updateParticipantRole = async (
  streamId: string,
  userId: string,
  newRole: StreamRole
) => {
  const participant = await db.streamParticipant.findUnique({
    where: {
      streamId_userId: {
        streamId,
        userId
      }
    }
  });

  if (!participant) {
    throw new Error("Participant not found");
  }

  return await db.streamParticipant.update({
    where: { id: participant.id },
    data: { role: newRole },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          imageURL: true,
        }
      }
    }
  });
};

export const getStreamInvitations = async (streamId: string) => {
  return await db.streamInvitation.findMany({
    where: {
      streamId,
      status: ParticipantStatus.INVITED,
      expiresAt: {
        gt: new Date()
      }
    },
    include: {
      invitee: {
        select: {
          id: true,
          username: true,
          imageURL: true,
        }
      },
      inviter: {
        select: {
          id: true,
          username: true,
        }
      }
    }
  });
};
