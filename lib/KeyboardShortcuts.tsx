'use client';

import { useEffect } from "react";
import { useLocalParticipant } from "@livekit/components-react";
import { Track } from "livekit-client";

export function KeyboardShortcuts() {
  const { localParticipant } = useLocalParticipant();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check for Cmd+Shift+A (Mac) or Ctrl+Shift+A (Windows/Linux)
      if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        toggleMicrophone();
      }

      // Check for Cmd+Shift+V (Mac) or Ctrl+Shift+V (Windows/Linux)
      if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key === 'V') {
        event.preventDefault();
        toggleCamera();
      }
    };

    const toggleMicrophone = async () => {
      if (!localParticipant) return;

      const micTrack = localParticipant.getTrackPublication(Track.Source.Microphone);
      if (micTrack) {
        await localParticipant.setMicrophoneEnabled(!micTrack.isEnabled);
      }
    };

    const toggleCamera = async () => {
      if (!localParticipant) return;

      const cameraTrack = localParticipant.getTrackPublication(Track.Source.Camera);
      if (cameraTrack) {
        await localParticipant.setCameraEnabled(!cameraTrack.isEnabled);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [localParticipant]);

  return null; // This component doesn't render anything
}
