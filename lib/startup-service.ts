import { resetAllStreamsToOffline } from "@/lib/livestream-service";

// Use a global flag that persists across module reloads in development
const globalForStartup = globalThis as unknown as {
  hasRunStartupCleanup: boolean | undefined;
};

export const runStartupCleanup = async () => {
    // Only run once per application lifecycle
    if (globalForStartup.hasRunStartupCleanup) {
        return;
    }

    try {
        console.log("[STARTUP_CLEANUP] Running startup cleanup...");

        // Reset all streams to offline status
        const resetCount = await resetAllStreamsToOffline();

        console.log(`[STARTUP_CLEANUP] Completed: Reset ${resetCount} streams to offline`);
        globalForStartup.hasRunStartupCleanup = true;

    } catch (error) {
        console.error("[STARTUP_CLEANUP_ERROR]", error);
        // Don't throw - we don't want startup cleanup to break the app
    }
};

// Reset the flag for testing purposes
export const resetStartupCleanupFlag = () => {
    globalForStartup.hasRunStartupCleanup = false;
};
