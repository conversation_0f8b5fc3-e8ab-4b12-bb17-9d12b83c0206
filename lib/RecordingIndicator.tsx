"use client";

import { useIsRecording } from "@livekit/components-react";
import { Circle } from "lucide-react";

export function RecordingIndicator() {
  const isRecording = useIsRecording();

  if (!isRecording) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 flex items-center space-x-2 bg-red-500 text-white px-3 py-2 rounded-lg shadow-lg">
      <Circle className="h-3 w-3 fill-current animate-pulse" />
      <span className="text-sm font-medium">Recording</span>
    </div>
  );
}
