import { db } from "@/lib/db";

export const getActiveLivestreams = async () => {
    try {
        const streams = await db.stream.findMany({
            where: {
                isLive: true,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        imageURL: true,
                    },
                },
            },
            orderBy: {
                updatedAt: "desc",
            },
        });

        return streams;
    } catch (error) {
        console.error("[GET_ACTIVE_LIVESTREAMS_ERROR]", error);
        return [];
    }
};

export const hasActiveLivestreams = async () => {
    try {
        const count = await db.stream.count({
            where: {
                isLive: true,
            },
        });

        return count > 0;
    } catch (error) {
        console.error("[HAS_ACTIVE_LIVESTREAMS_ERROR]", error);
        return false;
    }
};

export const resetAllStreamsToOffline = async () => {
    try {
        const result = await db.stream.updateMany({
            where: {
                isLive: true,
            },
            data: {
                isLive: false,
            },
        });

        console.log(`[RESET_STREAMS] Reset ${result.count} streams to offline`);
        return result.count;
    } catch (error) {
        console.error("[RESET_STREAMS_ERROR]", error);
        return 0;
    }
};

export const setStreamLiveStatus = async (streamId: string, isLive: boolean) => {
    try {
        const stream = await db.stream.update({
            where: { id: streamId },
            data: { isLive },
        });

        console.log(`[SET_STREAM_STATUS] Stream ${streamId} set to ${isLive ? 'live' : 'offline'}`);
        return stream;
    } catch (error) {
        console.error("[SET_STREAM_STATUS_ERROR]", error);
        throw error;
    }
};