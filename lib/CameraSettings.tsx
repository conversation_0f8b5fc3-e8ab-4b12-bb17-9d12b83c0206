import React from 'react';
import {
    MediaDeviceMenu,
    TrackReference,
    TrackToggle,
    useLocalParticipant,
    VideoTrack,
} from '@livekit/components-react';
import { BackgroundBlur, VirtualBackground } from '@livekit/track-processors';
import { isLocalTrack, LocalTrackPublication, Track } from 'livekit-client';
import { Button } from '@/components/ui/button';

// Background image paths - we'll use placeholder URLs for now
const BACKGROUND_IMAGES = [
    { name: 'Office', path: '/virtual-backgrounds/office.jpg' },
    { name: 'Nature', path: '/virtual-backgrounds/nature.jpg' },
    { name: 'Abstract', path: '/virtual-backgrounds/abstract.jpg' },
];

// Background options
type BackgroundType = 'none' | 'blur' | 'image';

export function CameraSettings() {
    const { cameraTrack, localParticipant } = useLocalParticipant();
    const [backgroundType, setBackgroundType] = React.useState<BackgroundType>(
        (cameraTrack as LocalTrackPublication)?.track?.getProcessor()?.name === 'background-blur'
            ? 'blur'
            : (cameraTrack as LocalTrackPublication)?.track?.getProcessor()?.name === 'virtual-background'
                ? 'image'
                : 'none',
    );

    const [virtualBackgroundImagePath, setVirtualBackgroundImagePath] = React.useState<string | null>(
        null,
    );

    const camTrackRef: TrackReference | undefined = React.useMemo(() => {
        return cameraTrack
            ? { participant: localParticipant, publication: cameraTrack, source: Track.Source.Camera }
            : undefined;
    }, [localParticipant, cameraTrack]);

    const selectBackground = (type: BackgroundType, imagePath?: string) => {
        setBackgroundType(type);
        if (type === 'image' && imagePath) {
            setVirtualBackgroundImagePath(imagePath);
        } else if (type !== 'image') {
            setVirtualBackgroundImagePath(null);
        }
    };

    React.useEffect(() => {
        if (isLocalTrack(cameraTrack?.track)) {
            if (backgroundType === 'blur') {
                cameraTrack.track?.setProcessor(BackgroundBlur());
            } else if (backgroundType === 'image' && virtualBackgroundImagePath) {
                cameraTrack.track?.setProcessor(VirtualBackground(virtualBackgroundImagePath));
            } else {
                cameraTrack.track?.stopProcessor();
            }
        }
    }, [cameraTrack, backgroundType, virtualBackgroundImagePath]);

    return (
        <div className="flex flex-col gap-4">
            {camTrackRef && (
                <VideoTrack
                    className="max-h-[280px] object-contain object-right transform scale-x-[-1] rounded-lg border"
                    trackRef={camTrackRef}
                />
            )}

            <div className="flex items-center space-x-2">
                <TrackToggle source={Track.Source.Camera} />
                <span className="text-muted-foreground">Camera</span>
                <div className="ml-auto">
                    <MediaDeviceMenu kind="videoinput" />
                </div>
            </div>

            <div className="mt-4">
                <div className="mb-2 text-sm font-medium">Background Effects</div>
                <div className="flex gap-2 flex-wrap">
                    <Button
                        onClick={() => selectBackground('none')}
                        variant={backgroundType === 'none' ? 'default' : 'outline'}
                        size="sm"
                        className="min-w-[80px]"
                    >
                        None
                    </Button>

                    <Button
                        onClick={() => selectBackground('blur')}
                        variant={backgroundType === 'blur' ? 'default' : 'outline'}
                        size="sm"
                        className="min-w-[80px] h-[60px] relative overflow-hidden"
                    >
                        <div className="absolute inset-0 bg-muted blur-sm" />
                        <span className="relative z-10 bg-black/60 px-2 py-1 rounded text-xs">
                            Blur
                        </span>
                    </Button>

                    {BACKGROUND_IMAGES.map((image) => (
                        <Button
                            key={image.path}
                            onClick={() => selectBackground('image', image.path)}
                            variant={
                                backgroundType === 'image' && virtualBackgroundImagePath === image.path
                                    ? 'default'
                                    : 'outline'
                            }
                            size="sm"
                            className="w-[80px] h-[60px] p-0 overflow-hidden"
                            style={{
                                backgroundImage: `url(${image.path})`,
                                backgroundSize: 'cover',
                                backgroundPosition: 'center',
                            }}
                        >
                            <span className="bg-black/60 px-2 py-1 rounded text-xs">
                                {image.name}
                            </span>
                        </Button>
                    ))}
                </div>
            </div>
        </div>
    );
} 