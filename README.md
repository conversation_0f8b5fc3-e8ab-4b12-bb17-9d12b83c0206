# 🎥 PopCircle - Multihost Livestreaming Service

<div align="center">
  <img src="public/popcircle.svg" alt="PopCircle Logo" width="120" height="120">

  **Connect, stream, and engage with PopCircle's modern multihost video platform**

  [![Live Demo](https://img.shields.io/badge/🌐_Live_Demo-ragna.day-orange?style=for-the-badge)](https://ragna.day)
  [![Vercel Deploy](https://img.shields.io/badge/🚀_Vercel-building--popcircle.vercel.app-blue?style=for-the-badge)](https://building-popcircle.vercel.app)
</div>

---

## ✨ What is PopCircle?

PopCircle is a **next-generation multihost livestreaming platform** that revolutionizes how creators and audiences interact. Unlike traditional single-host streaming platforms, PopCircle enables **multiple participants** to join streams as co-hosts, guests, or interactive viewers, creating dynamic, collaborative live experiences.

### 🎯 Key Features

- **🎪 Multihost Streaming** - Multiple participants can join as hosts, co-hosts, or guests
- **🎭 Role-Based Permissions** - Granular control over who can publish video, audio, and screen shares
- **💬 Real-Time Chat** - Integrated chat system with moderation controls
- **🔐 Flexible Authentication** - Support for both authenticated users and anonymous viewers
- **🎥 Screen Sharing** - Hosts and co-hosts can share screens during streams
- **RTMP Ingest** - Ingest streams from OBS or StreamLabs for Streamers used to Twitch or Youtube Live
- **Recordings (WIP)** - Automatic stream recordings for later viewing
- **Community (WIP)** - Follow and connect with other streamers and viewers in the PopCircle community
- **Moderation (WIP)** - Tools for managing chat and participant behavior, or blocking users from joining streams

---

## 🏗️ Architecture

PopCircle is built on a modern, scalable architecture:

```mermaid
graph TB
    A[Next.js Frontend] --> B[Clerk Authentication]
    A --> C[LiveKit WebRTC]
    A --> D[Prisma ORM]
    D --> E[CockroachDB]
    C --> F[LiveKit Cloud]
    A --> G[Real-time Chat]

    subgraph "User Roles"
        H[Host] --> I[Co-Host]
        I --> J[Guest]
        J --> K[Viewer]
    end

    subgraph "Streaming Features"
        L[Video Publishing]
        M[Screen Sharing]
        N[Audio Control]
        O[Chat Moderation]
    end
```

### 🛠️ Tech Stack

| Category            | Technology                              |
| ------------------- | --------------------------------------- |
| **Frontend**        | Next.js 15, React 18, TypeScript        |
| **Styling**         | Tailwind CSS v4, TweakCN (doom64 theme) |
| **Authentication**  | Clerk                                   |
| **Real-time Video** | LiveKit WebRTC                          |
| **Database**        | Prisma ORM + CockroachDB                |
| **UI Components**   | Radix UI, shadcn/ui                     |
| **Deployment**      | Vercel, Cloudflare Workers              |

---

## 🚀 Live Deployments

- **Production (Soon)**: [popcircle.com](https://popcircle.com)
- **Test Domain**: [ragna.day](https://ragna.day)
- **Development**: [building-popcircle.vercel.app](https://building-popcircle.vercel.app)

---

## 🎮 How It Works

### 1. **Stream Creation**
- Authenticated users can create streams instantly
- Each stream gets a unique room with configurable settings
- Hosts control participant limits and approval requirements

### 2. **Multi-Participant System**
```typescript
enum StreamRole {
  HOST      // Full control: publish video/audio/screen, moderate chat
  CO_HOST   // Publish video/audio/screen, limited moderation
  GUEST     // Publish video/audio only
  VIEWER    // Watch and chat only
}
```

### 3. **Real-Time Permissions**
- Dynamic role assignment during streams
- Granular permissions for video, audio, screen sharing
- Anonymous viewers can watch without registration

### 4. **Interactive Features**
- Live chat with moderation tools
- Participant invitation system
- Screen sharing capabilities
- Mobile-responsive video layouts

---

## 🛠️ Getting Started

### Prerequisites
- Node.js 18+
- npm/yarn/pnpm
- CockroachDB instance (or any other postgres-compatible database)
- LiveKit Cloud account
- Clerk authentication setup

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/daniel94fuller/building-popcircle.git
cd building-popcircle
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Setup**
Create a `.env` file by copying `.env.example` and filling in the required values.

4. **Database Setup**
```bash
npx prisma generate
npx prisma db push
```

5. **Start Development Server**
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see PopCircle in action!

---

## 🎪 Platform Features

### 🎭 User Roles & Permissions

| Role        | Video | Audio | Screen Share | Chat Moderation | Invite Users |
| ----------- | ----- | ----- | ------------ | --------------- | ------------ |
| **Host**    | ✅     | ✅     | ✅            | ✅               | ✅            |
| **Co-Host** | ✅     | ✅     | ✅            | ⚠️ Limited       | ✅            |
| **Guest**   | ✅     | ✅     | ❌            | ❌               | ❌            |
| **Viewer**  | ❌     | ❌     | ❌            | ❌               | ❌            |

### 🔧 Stream Management
- **Dynamic Participant Control** - Add/remove participants during live streams
- **Permission Management** - Real-time role changes and permission updates
- **Stream Settings** - Configure max participants, guest approval, chat settings
- **Anonymous Viewing** - No signup required for viewers
- **Mobile Optimization** - Responsive design for all devices

### 💬 Chat System
- **Real-time Messaging** - Instant chat with all participants
- **Moderation Tools** - Message filtering and user management
- **Follower-only Mode** - Restrict chat to followers
- **Chat Delay** - Optional delay for moderation
- **Rich Text Support** - Links and formatting in messages


---

## 📁 Project Structure

```
building-popcircle/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── (browse)/          # Public browsing pages
│   ├── (dashboard)/       # User dashboard
│   └── api/               # API routes
├── components/            # Reusable UI components
│   ├── stream-player/     # Video streaming components
│   └── ui/                # shadcn/ui components
├── lib/                   # Utility functions and services
├── prisma/                # Database schema and migrations
├── actions/               # Server actions
└── store/                 # Zustand state management
```

---

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Deploy to Vercel
npm run build
vercel --prod
```

### Environment Variables
Ensure these are set in your deployment environment:
- `DATABASE_URL`
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`
- `CLERK_SECRET_KEY`
- `NEXT_PUBLIC_LIVEKIT_WS_URL`
- `LIVEKIT_API_KEY`
- `LIVEKIT_API_SECRET`

---

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Use TypeScript for all new code
- Follow the PopCircle theme guidelines
- Test multihost functionality thoroughly
- Ensure mobile responsiveness

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 💖 Acknowledgments

- **[LiveKit](https://livekit.io/)** - Real-time video infrastructure
- **[Clerk](https://clerk.dev/)** - Authentication and user management
- **[Vercel](https://vercel.com/)** - Deployment and hosting
- **[TweakCN](https://tweakcn.com/)** - Theme system and design tokens

---

<div align="center">
  <p>
    <a href="https://ragna.day">🌐 Live Demo</a> •
    <a href="https://building-popcircle.vercel.app">🚀 Development</a> •
    <a href="#-getting-started">📖 Documentation</a>
  </p>
</div>
