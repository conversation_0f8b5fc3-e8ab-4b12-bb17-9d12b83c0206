"use client";

import { User } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Video, Users, Clock, UserPlus, Play } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";

interface RecordedSessionWithHost {
    id: string;
    title: string;
    description: string | null;
    thumbnailUrl: string | null;
    duration: number | null;
    participantCount: number;
    publishedAt: Date | null;
    host: {
        id: string;
        username: string;
        imageURL: string | null;
    };
}

interface RecordedContentLandingProps {
    recordedSessions: RecordedSessionWithHost[];
    currentUser?: User | null;
}

const formatDuration = (seconds: number | null) => {
    if (!seconds) return "Unknown";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const formatDate = (date: Date | null) => {
    if (!date) return "Unknown";
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
        Math.ceil((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
        'day'
    );
};

export const RecordedContentLanding = ({
    recordedSessions,
    currentUser,
}: RecordedContentLandingProps) => {
    const hasRecordedContent = recordedSessions.length > 0;
    const [roomName, setRoomName] = useState("");
    const router = useRouter();

    const handleJoinRoom = (e: React.FormEvent) => {
        e.preventDefault();
        if (roomName.trim()) {
            router.push(`/rooms/${roomName.trim()}`);
        }
    };

    if (!hasRecordedContent) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
                <div className="text-center space-y-6 max-w-md">
                    <div className="w-20 h-20 mx-auto bg-muted rounded-full flex items-center justify-center">
                        <Video className="w-10 h-10 text-muted-foreground" />
                    </div>

                    <div className="space-y-2">
                        <h2 className="text-2xl font-bold text-foreground">
                            No recorded content yet
                        </h2>
                        <p className="text-muted-foreground">
                            Start creating sessions to build your content library
                        </p>
                    </div>

                    {currentUser ? (
                        <Link href={`/u/${currentUser.username}`}>
                            <Button
                                size="lg"
                                className="bg-red-600 hover:bg-red-700 text-white px-8 py-3"
                            >
                                <Video className="w-5 h-5 mr-2" />
                                Create Session
                            </Button>
                        </Link>
                    ) : (
                        <Link href="/sign-in">
                            <Button
                                size="lg"
                                className="bg-red-600 hover:bg-red-700 text-white px-8 py-3"
                            >
                                <Video className="w-5 h-5 mr-2" />
                                Sign in to Create
                            </Button>
                        </Link>
                    )}
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 space-y-6">
            {/* Join Room Section */}
            <div className="bg-card border border-border rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                    <div>
                        <h3 className="text-lg font-semibold text-card-foreground flex items-center">
                            <UserPlus className="w-5 h-5 mr-2" />
                            Join a Live Session
                        </h3>
                        <p className="text-muted-foreground text-sm">
                            Enter a creator&apos;s username to join their live session
                        </p>
                    </div>
                </div>
                <form onSubmit={handleJoinRoom} className="flex space-x-3">
                    <Input
                        type="text"
                        placeholder="Enter creator username..."
                        value={roomName}
                        onChange={(e) => setRoomName(e.target.value)}
                        className="flex-1"
                    />
                    <Button
                        type="submit"
                        disabled={!roomName.trim()}
                        className="bg-primary hover:bg-primary/90 text-primary-foreground"
                    >
                        Join Session
                    </Button>
                </form>
            </div>

            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold text-foreground">Recorded Sessions</h2>
                    <p className="text-muted-foreground">
                        {recordedSessions.length} recorded session{recordedSessions.length === 1 ? "" : "s"}{" "}
                        available
                    </p>
                </div>

                {currentUser && (
                    <Link href={`/u/${currentUser.username}`}>
                        <Button className="bg-destructive hover:bg-destructive/90 text-destructive-foreground">
                            <Video className="w-4 h-4 mr-2" />
                            Create Session
                        </Button>
                    </Link>
                )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recordedSessions.map((session) => (
                    <Link key={session.id} href={`/watch/${session.id}`}>
                        <div className="bg-card border border-border rounded-lg hover:bg-accent/10 transition-colors cursor-pointer p-4">
                            <div className="pb-3">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                                            {session.host.imageURL ? (
                                                <Image
                                                    src={session.host.imageURL}
                                                    alt={session.host.username}
                                                    width={40}
                                                    height={40}
                                                    className="w-10 h-10 rounded-full object-cover"
                                                />
                                            ) : (
                                                <span className="text-sm font-medium text-card-foreground">
                                                    {session.host.username.charAt(0).toUpperCase()}
                                                </span>
                                            )}
                                        </div>
                                        <div>
                                            <h3 className="text-sm font-semibold text-card-foreground">
                                                {session.title}
                                            </h3>
                                            <p className="text-xs text-muted-foreground">
                                                @{session.host.username}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                        <Play className="w-3 h-3 text-primary" />
                                        <span className="text-xs font-medium text-primary">
                                            RECORDED
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div className="pt-0">
                                <div className="aspect-video bg-muted rounded-lg flex items-center justify-center mb-3 relative">
                                    {session.thumbnailUrl ? (
                                        <Image
                                            src={session.thumbnailUrl}
                                            alt={session.title}
                                            fill
                                            className="object-cover rounded-lg"
                                        />
                                    ) : (
                                        <Video className="w-8 h-8 text-muted-foreground" />
                                    )}
                                    <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                                        <Play className="w-8 h-8 text-white" />
                                    </div>
                                </div>

                                <div className="flex items-center justify-between text-xs text-muted-foreground">
                                    <div className="flex items-center space-x-1">
                                        <Users className="w-3 h-3" />
                                        <span>{session.participantCount} participant{session.participantCount === 1 ? "" : "s"}</span>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                        <Clock className="w-3 h-3" />
                                        <span>{formatDuration(session.duration)}</span>
                                    </div>
                                </div>

                                {session.publishedAt && (
                                    <div className="text-xs text-muted-foreground mt-1">
                                        Published {formatDate(session.publishedAt)}
                                    </div>
                                )}
                            </div>
                        </div>
                    </Link>
                ))}
            </div>
        </div>
    );
}; 