'use client';

import { formatChatMessageLinks, RoomContext, VideoConference } from '@livekit/components-react';
import {
  LogLevel,
  Room,
  RoomConnectOptions,
  RoomOptions,
  VideoPresets,
  type VideoCodec,
  RoomEvent,
} from 'livekit-client';
import { useEffect, useMemo, useState, useCallback } from 'react';
import { ConnectionDetails } from '@/lib/client-utils';
import { LocalUserChoices } from '@livekit/components-react';
import { User, Stream } from '@prisma/client';

import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { KeyboardShortcuts } from '@/lib/KeyboardShortcuts';
import { DebugMode } from '@/lib/Debug';
import { SettingsMenu } from '@/lib/SettingsMenu';
import { RecordingIndicator } from '@/lib/RecordingIndicator';

export function VideoConferenceComponent(props: {
  connectionDetails: ConnectionDetails;
  userChoices: LocalUserChoices;
  options: {
    hq: boolean;
    codec: VideoCodec;
  };
  hostUser: User;
  stream: Stream;
}) {
  const { user } = useUser();
  const router = useRouter();
  // const keyProvider = useMemo(() => new ExternalE2EEKeyProvider(), []);
  const [e2eeSetupComplete, setE2eeSetupComplete] = useState(false);

  // For now, disable E2EE to simplify setup
  const e2eeEnabled = false;

  const roomOptions = useMemo((): RoomOptions => {
    return {
      publishDefaults: {
        videoSimulcastLayers: [VideoPresets.h540, VideoPresets.h216],
        red: !e2eeEnabled,
        videoCodec: props.options.codec,
      },
      adaptiveStream: { pixelDensity: 'screen' },
      dynacast: true,
      e2ee: undefined, // Disable E2EE for now
    };
  }, [e2eeEnabled, props.options.codec]);

  const room = useMemo(() => new Room(roomOptions), [roomOptions]);

  const connectOptions = useMemo((): RoomConnectOptions => {
    return {
      autoSubscribe: true,
    };
  }, []);

  // Event handlers (like Meet)
  const handleOnLeave = useCallback(async () => {
    // If this is the host leaving, mark stream as offline
    const isHost = user?.id === props.hostUser.externalUserId;
    if (isHost) {
      try {
        await fetch('/api/stream-end', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            roomName: props.hostUser.id,
          }),
        });
      } catch (error) {
        console.error('Failed to mark stream as offline:', error);
      }
    }
    router.push('/');
  }, [router, user?.id, props.hostUser.externalUserId, props.hostUser.id]);

  const handleError = useCallback((error: Error) => {
    console.error('Room error:', error);
    toast.error(`Connection error: ${error.message}`);
  }, []);

  const handleEncryptionError = useCallback((error: Error) => {
    console.error('Encryption error:', error);
    toast.error(`Encryption error: ${error.message}`);
  }, []);

  useEffect(() => {
    setE2eeSetupComplete(true);
  }, []);

  // Handle room events to update stream status
  const handleRoomConnected = useCallback(async () => {
    // Only update stream status if this is the host
    const isHost = user?.id === props.hostUser?.externalUserId;
    if (isHost && props.hostUser) {
      try {
        await fetch('/api/stream-status', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ hostIdentity: props.hostUser.id, isLive: true })
        });
        console.log('[STREAM_STATUS] Stream marked as live');
      } catch (error) {
        console.error('[STREAM_STATUS] Failed to mark stream as live:', error);
      }
    }
  }, [user?.id, props.hostUser]);

  const handleRoomDisconnected = useCallback(async () => {
    // Only update stream status if this is the host
    const isHost = user?.id === props.hostUser?.externalUserId;
    if (isHost && props.hostUser) {
      try {
        await fetch('/api/stream-status', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ hostIdentity: props.hostUser.id, isLive: false })
        });
        console.log('[STREAM_STATUS] Stream marked as offline');
      } catch (error) {
        console.error('[STREAM_STATUS] Failed to mark stream as offline:', error);
      }
    }
  }, [user?.id, props.hostUser]);

  useEffect(() => {
    // Set up event listeners (like Meet)
    room.on(RoomEvent.Connected, handleRoomConnected);
    room.on(RoomEvent.Disconnected, handleOnLeave);
    room.on(RoomEvent.Disconnected, handleRoomDisconnected);
    room.on(RoomEvent.EncryptionError, handleEncryptionError);
    room.on(RoomEvent.MediaDevicesError, handleError);

    if (e2eeSetupComplete) {
      room.connect(
        props.connectionDetails.serverUrl,
        props.connectionDetails.participantToken,
        connectOptions
      ).catch((error) => {
        handleError(error);
      });

      // Enable camera and microphone individually based on user choices (like Meet)
      if (props.userChoices.videoEnabled) {
        room.localParticipant.setCameraEnabled(true).catch((error) => {
          handleError(error);
        });
      }
      if (props.userChoices.audioEnabled) {
        room.localParticipant.setMicrophoneEnabled(true).catch((error) => {
          handleError(error);
        });
      }
    }

    return () => {
      // Clean up stream status on unmount
      handleRoomDisconnected();
      room.off(RoomEvent.Connected, handleRoomConnected);
      room.off(RoomEvent.Disconnected, handleOnLeave);
      room.off(RoomEvent.Disconnected, handleRoomDisconnected);
      room.off(RoomEvent.EncryptionError, handleEncryptionError);
      room.off(RoomEvent.MediaDevicesError, handleError);
    };
  }, [e2eeSetupComplete, room, props.connectionDetails, props.userChoices, connectOptions, handleOnLeave, handleError, handleEncryptionError, handleRoomConnected, handleRoomDisconnected]);

  // Check if current user is the host
  // const isHost = user?.id === props.hostUser.externalUserId;
  // const isFollowing = false; // We'll implement this later

  return (
    <div className="h-full" data-lk-theme="default">
      <RoomContext.Provider value={room}>
        <KeyboardShortcuts />
        <div className="lk-room-container h-full relative">
          <VideoConference
            chatMessageFormatter={formatChatMessageLinks}
            SettingsComponent={SettingsMenu}
          />
          <RecordingIndicator />
        </div>
        <DebugMode logLevel={LogLevel.debug} />
      </RoomContext.Provider>
    </div>
  );
}
