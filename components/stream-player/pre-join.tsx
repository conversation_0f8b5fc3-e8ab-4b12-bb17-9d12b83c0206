"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Mic, MicOff, Video, VideoOff } from 'lucide-react';
import { LocalUserChoices } from '@livekit/components-react';

interface PreJoinProps {
  defaults?: Partial<LocalUserChoices>;
  onSubmit: (choices: LocalUserChoices) => void;
  onError?: (error: Error) => void;
  participantName?: string;
  roomName?: string;
}

export const PreJoin: React.FC<PreJoinProps> = ({
  defaults = {},
  onSubmit,
  onError,
  participantName: initialParticipantName = '',
  roomName = ''
}) => {
  const [participantName, setParticipantName] = useState(initialParticipantName);
  const [audioEnabled, setAudioEnabled] = useState(defaults.audioEnabled ?? true);
  const [videoEnabled, setVideoEnabled] = useState(defaults.videoEnabled ?? true);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!participantName.trim()) {
      onError?.(new Error('Please enter your name'));
      return;
    }

    setIsLoading(true);

    try {
      const choices: LocalUserChoices = {
        username: participantName.trim(),
        videoEnabled,
        audioEnabled,
        videoDeviceId: defaults.videoDeviceId || '',
        audioDeviceId: defaults.audioDeviceId || '',
      };

      onSubmit(choices);
    } catch (error) {
      onError?.(error instanceof Error ? error : new Error('Failed to join room'));
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle>Join Stream</CardTitle>
          <CardDescription>
            {roomName ? `Joining ${roomName}` : 'Configure your settings before joining'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="participantName">Your Name</Label>
              <Input
                id="participantName"
                type="text"
                value={participantName}
                onChange={(e) => setParticipantName(e.target.value)}
                placeholder="Enter your name"
                required
                disabled={isLoading}
              />
            </div>

            <div className="space-y-4">
              <Label>Media Settings</Label>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {audioEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                  <span>Microphone</span>
                </div>
                <Button
                  type="button"
                  variant={audioEnabled ? "default" : "outline"}
                  size="sm"
                  onClick={() => setAudioEnabled(!audioEnabled)}
                  disabled={isLoading}
                >
                  {audioEnabled ? 'On' : 'Off'}
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {videoEnabled ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                  <span>Camera</span>
                </div>
                <Button
                  type="button"
                  variant={videoEnabled ? "default" : "outline"}
                  size="sm"
                  onClick={() => setVideoEnabled(!videoEnabled)}
                  disabled={isLoading}
                >
                  {videoEnabled ? 'On' : 'Off'}
                </Button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || !participantName.trim()}
            >
              {isLoading ? 'Joining...' : 'Join Stream'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
