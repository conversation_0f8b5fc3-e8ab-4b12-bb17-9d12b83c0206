"use client";

import { format } from "date-fns";
import { ReceivedChatMessage } from "@livekit/components-react";

import { stringToColor } from "@/lib/utils";

interface ChatMessageProps {
  data: ReceivedChatMessage;
}

export const ChatMessage = ({ data }: ChatMessageProps) => {
  // Get username from the participant or fallback to message data
  const username = data.from?.name || data.from?.identity || "Unknown";
  const color = stringToColor(username);

  return (
    <div className="flex gap-2 p-2 rounded-md hover:bg-muted/50">
      <p className="text-basem text-baseuted-foreground">
        {format(data.timestamp, "HH:MM")}
      </p>
      <div className="flex flex-wrap items-baseline gap-1 flex-grow">
        <p className="text-basem font-semibold" style={{ color }}>
          {username}
        </p>
        <p className="text-basem text-foreground break-all">{data.message}</p>
      </div>
    </div>
  );
};
