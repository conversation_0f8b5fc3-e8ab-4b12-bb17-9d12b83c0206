"use client";

import {
  VideoConference,
  formatChatMessageLinks
} from "@livekit/components-react";
import { cn } from "@/lib/utils";
import { RecordingControls } from "./recording-controls";

interface VideoConferenceWrapperProps {
  className?: string;
  hostIdentity?: string;
}

export const VideoConferenceWrapper = ({
  className,
  hostIdentity
}: VideoConferenceWrapperProps) => {
  return (
    <div
      className={cn(
        "relative w-full bg-background rounded-lg overflow-hidden",
        "border border-border shadow-lg",
        "min-h-[500px] max-h-[80vh]", // Set reasonable height constraints
        className
      )}
      data-lk-theme="default"
      style={{
        height: '600px', // Fixed height to prevent layout issues
        position: 'relative',
        isolation: 'isolate', // Create new stacking context
        backgroundColor: 'var(--background)',
        borderColor: 'var(--border)',
        borderRadius: 'var(--radius)',
        boxShadow: 'var(--shadow-lg)',
      }}
    >
      {/* Recording Controls */}
      {hostIdentity && (
        <div className="absolute top-4 right-4 z-10">
          <RecordingControls hostIdentity={hostIdentity} />
        </div>
      )}

      <div
        className="lk-room-container w-full h-full bg-background text-foreground"
        style={{
          height: '100%',
          width: '100%',
          position: 'relative',
          overflow: 'hidden',
          fontFamily: 'var(--font-sans)',
        }}
      >
        <VideoConference
          chatMessageFormatter={formatChatMessageLinks}
        />
      </div>
    </div>
  );
};
