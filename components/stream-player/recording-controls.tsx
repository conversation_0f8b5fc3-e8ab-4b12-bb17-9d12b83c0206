"use client";

import { useState, useCallback, useContext } from "react";
import { Button } from "@/components/ui/button";
import { useUser } from "@clerk/nextjs";
import { RoomContext } from "@livekit/components-react";
import { Circle, Square, Loader2 } from "lucide-react";
import { toast } from "sonner";

interface RecordingControlsProps {
    hostIdentity: string;
    className?: string;
}

export const RecordingControls = ({
    hostIdentity,
    className
}: RecordingControlsProps) => {
    const { user } = useUser();
    const room = useContext(RoomContext);
    const [isRecording, setIsRecording] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [currentSession, setCurrentSession] = useState<{
        sessionId: string;
        egressId: string;
    } | null>(null);

    // Only show controls if user is the host
    const isHost = user?.id && hostIdentity && user.id === hostIdentity;

    const startRecording = useCallback(async () => {
        if (!room || !isHost) return;

        setIsLoading(true);
        try {
            const response = await fetch('/api/recording/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    roomName: hostIdentity, // Use hostIdentity (external user ID)
                    title: `${user?.username || 'Host'}'s Session`,
                    description: `Recorded session from ${new Date().toLocaleDateString()}`,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to start recording');
            }

            setCurrentSession({
                sessionId: data.sessionId,
                egressId: data.egressId,
            });
            setIsRecording(true);
            toast.success('Recording started successfully');

        } catch (error) {
            console.error('Failed to start recording:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to start recording');
        } finally {
            setIsLoading(false);
        }
    }, [room, isHost, user?.username, hostIdentity]);

    const stopRecording = useCallback(async () => {
        if (!currentSession) return;

        setIsLoading(true);
        try {
            const response = await fetch('/api/recording/stop', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    egressId: currentSession.egressId,
                    sessionId: currentSession.sessionId,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to stop recording');
            }

            setIsRecording(false);
            setCurrentSession(null);
            toast.success('Recording stopped successfully');

        } catch (error) {
            console.error('Failed to stop recording:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to stop recording');
        } finally {
            setIsLoading(false);
        }
    }, [currentSession]);

    // Don't render if not host
    if (!isHost) {
        return null;
    }

    return (
        <div className={`flex items-center space-x-2 ${className}`}>
            {!isRecording ? (
                <Button
                    onClick={startRecording}
                    disabled={isLoading}
                    variant="destructive"
                    size="sm"
                    className="flex items-center space-x-2"
                >
                    {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                        <Circle className="h-4 w-4 fill-current" />
                    )}
                    <span>{isLoading ? 'Starting...' : 'Start Recording'}</span>
                </Button>
            ) : (
                <Button
                    onClick={stopRecording}
                    disabled={isLoading}
                    variant="secondary"
                    size="sm"
                    className="flex items-center space-x-2"
                >
                    {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                        <Square className="h-4 w-4 fill-current" />
                    )}
                    <span>{isLoading ? 'Stopping...' : 'Stop Recording'}</span>
                </Button>
            )}

            {isRecording && (
                <div className="flex items-center space-x-1 text-sm text-red-500">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                    <span>Recording</span>
                </div>
            )}
        </div>
    );
}; 