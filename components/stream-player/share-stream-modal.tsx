"use client";

import { useState, useTransition } from "react";
import { toast } from "sonner";
import { Copy, Share2, Users, ExternalLink } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { getStreamShareLink } from "@/actions/stream";

interface ShareStreamModalProps {
  streamId?: string;
  trigger?: React.ReactNode;
}

export const ShareStreamModal = ({
  streamId,
  trigger,
}: ShareStreamModalProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [shareData, setShareData] = useState<{
    shareLink: string;
    streamName: string;
    hostUsername: string;
    isLive: boolean;
  } | null>(null);
  const [isPending, startTransition] = useTransition();

  const handleGetShareLink = () => {
    startTransition(async () => {
      try {
        const data = await getStreamShareLink(streamId);
        setShareData(data);
      } catch (error) {
        console.error("Failed to get share link:", error);
        toast.error("Failed to generate share link");
      }
    });
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${label} copied to clipboard!`);
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error("Failed to copy to clipboard");
    }
  };

  const handleOpenModal = () => {
    setIsOpen(true);
    if (!shareData) {
      handleGetShareLink();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" onClick={handleOpenModal}>
            <Share2 className="h-4 w-4 mr-2" />
            Share Stream
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Share Your Stream
          </DialogTitle>
          <DialogDescription>
            Share your stream link with viewers so they can join and watch.
          </DialogDescription>
        </DialogHeader>

        {isPending ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-basem text-baseuted-foreground">
                Generating share link...
              </p>
            </div>
          </div>
        ) : shareData ? (
          <div className="space-y-4">
            {/* Stream Info */}
            <div className="bg-muted/50 p-3 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{shareData.streamName}</p>
                  <p className="text-basem text-baseuted-foreground">
                    @{shareData.hostUsername}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className={`h-2 w-2 rounded-full ${
                      shareData.isLive
                        ? "bg-destructive animate-pulse"
                        : "bg-muted-foreground"
                    }`}
                  />
                  <span className="text-xs font-medium">
                    {shareData.isLive ? "LIVE" : "OFFLINE"}
                  </span>
                </div>
              </div>
            </div>

            {/* Share Link */}
            <div className="space-y-2">
              <Label htmlFor="share-link">Stream Link</Label>
              <div className="flex gap-2">
                <Input
                  id="share-link"
                  value={shareData.shareLink}
                  readOnly
                  className="font-mono text-basem"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    copyToClipboard(shareData.shareLink, "Stream link")
                  }
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-baseuted-foreground">
                Anyone with this link can view your stream
              </p>
            </div>

            <Separator />

            {/* Quick Actions */}
            <div className="space-y-3">
              <Label>Quick Actions</Label>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(shareData.shareLink, "_blank")}
                  className="justify-start"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open Stream
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const text = `Check out my live stream: ${shareData.streamName}\n${shareData.shareLink}`;
                    copyToClipboard(text, "Share message");
                  }}
                  className="justify-start"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Copy Message
                </Button>
              </div>
            </div>

            {/* Social Share Buttons */}
            <div className="space-y-3">
              <Label>Share On</Label>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(
                      `🔴 LIVE: ${shareData.streamName}`
                    )}&url=${encodeURIComponent(shareData.shareLink)}`;
                    window.open(url, "_blank");
                  }}
                  className="justify-start"
                >
                  Twitter
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                      shareData.shareLink
                    )}`;
                    window.open(url, "_blank");
                  }}
                  className="justify-start"
                >
                  Facebook
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center py-8">
            <Button onClick={handleGetShareLink} disabled={isPending}>
              <Share2 className="h-4 w-4 mr-2" />
              Generate Share Link
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
