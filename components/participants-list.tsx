"use client";

import { useParticipants } from "@livekit/components-react";

export const ParticipantsList = () => {
  const participants = useParticipants();

  return (
    <div className="p-4">
      <h2 className="text-lg font-semibold mb-2">Participants</h2>
      <ul className="space-y-1">
        {participants.map((participant) => (
          <li key={participant.identity}>
            {participant.name || participant.identity}
            {participant.isSpeaking ? " 🔊" : ""}
            {participant.isLocal ? " (You)" : ""}
          </li>
        ))}
      </ul>
    </div>
  );
};
