import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
// import { Skeleton } from "@/components/ui/skeleton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { LiveBadge } from "./live-badge";

const avatarSizes = cva("", {
  variants: { size: { default: "h-10 w-10", lg: "h-14 w-14" } },
  defaultVariants: { size: "default" },
});

interface UserAvatarProps extends VariantProps<typeof avatarSizes> {
  username: string;
  imageUrl: string;
  isLive?: boolean;
  showBadge?: boolean;
}

export const UserAvatar = ({
  username,
  imageUrl,
  isLive,
  showBadge,
  size,
}: UserAvatarProps) => {
  const canShowBadge = showBadge && isLive;
  return (
    <div className="relative">
      <Avatar
        className={cn(
          "min-w-8 min-h-8", // fallback minimum
          isLive && "ring-2 ring-rose-500 border border-background",
          avatarSizes({ size })
        )}
      >
        <AvatarImage src={imageUrl} className="object-cover w-full h-full" />

        <AvatarFallback className="text-sm font-medium text-white bg-muted">
          {username[0]}
        </AvatarFallback>
      </Avatar>

      {canShowBadge && (
        <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
          <LiveBadge />
        </div>
      )}
    </div>
  );
};

// interface UserAvatarSkeletonProps extends VariantProps<typeof avatarSizes> {}
// export const UserAvatarSkeleton = ({
//   size,
// }: VariantProps<typeof avatarSizes>) => {
//   return <Skeleton className={cn("rounded-full", avatarSizes({ size }))} />;
// };
