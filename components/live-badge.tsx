import { cn } from "@/lib/utils";

interface LiveBadgeProps {
  className?: string;
}

export const LiveBadge = ({ className }: LiveBadgeProps) => {
  return (
    <div
      className={cn(
        "bg-destructive text-destructive-foreground text-center p-0.5 px-1.5 rounded-md uppercase text-[10px] border border-border font-semibold tracking-wide shadow-sm",
        "animate-pulse",
        className
      )}
    >
      Live
    </div>
  );
};
