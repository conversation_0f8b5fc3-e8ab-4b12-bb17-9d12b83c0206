"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Users, Plus, MessageCircle } from "lucide-react";
import { User } from "@prisma/client";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface ActiveHangout {
    id: string;
    name: string;
    host: User;
    participantCount: number;
    maxParticipants: number;
    participants: Array<{
        user: User;
    }>;
    createdAt: string;
}

interface HangoutLandingProps {
    activeHangouts: ActiveHangout[];
    currentUser: User | null;
}

export const HangoutLanding = ({
    activeHangouts,
    currentUser,
}: HangoutLandingProps) => {
    const [hangoutName, setHangoutName] = useState("");
    const [isCreating, setIsCreating] = useState(false);
    const router = useRouter();

    const handleCreateHangout = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!hangoutName.trim()) {
            toast.error("Please enter a hangout name");
            return;
        }

        setIsCreating(true);
        try {
            const response = await fetch('/api/hangout/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: hangoutName.trim(),
                }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || 'Failed to create hangout');
            }

            const { room } = await response.json();
            toast.success("Hangout created!");

            // Navigate to the new hangout room
            router.push(`/hangouts/${room.id}`);
        } catch (error) {
            console.error('Error creating hangout:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to create hangout');
        } finally {
            setIsCreating(false);
        }
    };

    const joinHangout = (hangoutId: string) => {
        router.push(`/hangouts/${hangoutId}`);
    };

    if (!currentUser) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
                <div className="text-center space-y-6 max-w-md">
                    <div className="w-20 h-20 mx-auto bg-muted rounded-full flex items-center justify-center">
                        <Users className="w-10 h-10 text-muted-foreground" />
                    </div>
                    <div className="space-y-2">
                        <h2 className="text-2xl font-bold text-foreground">
                            Join the conversation
                        </h2>
                        <p className="text-muted-foreground">
                            Sign in to start or join hangout rooms with friends
                        </p>
                    </div>
                    <Button
                        onClick={() => router.push('/sign-in')}
                        size="lg"
                        className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3"
                    >
                        <Users className="w-5 h-5 mr-2" />
                        Sign In to Join
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 space-y-6 max-w-7xl mx-auto">
            {/* Create New Hangout Section */}
            <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-2 border-dashed border-primary/20">
                <CardHeader className="text-center">
                    <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2">
                        <Plus className="w-6 h-6" />
                        Start a Hangout
                    </CardTitle>
                    <p className="text-muted-foreground">
                        Create a space for you and your friends to hang out
                    </p>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleCreateHangout} className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
                        <Input
                            type="text"
                            placeholder="Name your hangout..."
                            value={hangoutName}
                            onChange={(e) => setHangoutName(e.target.value)}
                            className="flex-1"
                            disabled={isCreating}
                        />
                        <Button
                            type="submit"
                            disabled={!hangoutName.trim() || isCreating}
                            className="bg-primary hover:bg-primary/90 text-primary-foreground"
                        >
                            {isCreating ? 'Creating...' : 'Start Hangout'}
                        </Button>
                    </form>
                </CardContent>
            </Card>

            {/* Active Hangouts */}
            <div>
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h2 className="text-2xl font-bold text-foreground">Active Hangouts</h2>
                        <p className="text-muted-foreground">
                            {activeHangouts.length} hangout{activeHangouts.length === 1 ? '' : 's'} happening now
                        </p>
                    </div>
                </div>

                {activeHangouts.length === 0 ? (
                    <Card className="text-center py-12">
                        <CardContent>
                            <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center mb-4">
                                <MessageCircle className="w-8 h-8 text-muted-foreground" />
                            </div>
                            <h3 className="text-lg font-semibold mb-2">No active hangouts</h3>
                            <p className="text-muted-foreground mb-4">
                                Be the first to start a hangout and bring people together!
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {activeHangouts.map((hangout) => (
                            <Card
                                key={hangout.id}
                                className="hover:shadow-lg transition-shadow duration-200 cursor-pointer group"
                                onClick={() => joinHangout(hangout.id)}
                            >
                                <CardHeader className="pb-3">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <CardTitle className="text-lg group-hover:text-primary transition-colors">
                                                {hangout.name}
                                            </CardTitle>
                                            <p className="text-sm text-muted-foreground mt-1">
                                                Hosted by {hangout.host.username}
                                            </p>
                                        </div>
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Users className="w-4 h-4 mr-1" />
                                            {hangout.participantCount}/{hangout.maxParticipants}
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="pt-0">
                                    <div className="flex -space-x-2 mb-3">
                                        {hangout.participants.slice(0, 5).map((participant) => (
                                            <Avatar key={participant.user.id} className="w-8 h-8 border-2 border-background">
                                                <AvatarImage src={participant.user.imageURL} />
                                                <AvatarFallback className="text-xs">
                                                    {participant.user.username.charAt(0).toUpperCase()}
                                                </AvatarFallback>
                                            </Avatar>
                                        ))}
                                        {hangout.participantCount > 5 && (
                                            <div className="w-8 h-8 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                                                <span className="text-xs font-medium">
                                                    +{hangout.participantCount - 5}
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                    <Button
                                        className="w-full group-hover:bg-primary/90"
                                        size="sm"
                                    >
                                        Join Hangout
                                    </Button>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}; 