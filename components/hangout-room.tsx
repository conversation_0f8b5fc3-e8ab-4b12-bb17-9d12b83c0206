"use client";

import { use<PERSON><PERSON>back, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User } from "@prisma/client";
import {
    LiveKitRoom,
    useParticipants,
    RoomAudioRenderer,
    ControlBar,
    useIsRecording,
    useRoomContext,
    useDataChannel
} from "@livekit/components-react";
import { useHangoutToken } from "@/hooks/use-hangout-token";
import { HangoutGridLayout } from "./hangout-grid-layout";
import { HangoutRecordingControls } from "./hangout-recording-controls";
import { useUser } from "@clerk/nextjs";
import { toast } from "sonner";
import { LogOut, X } from "lucide-react";
import { RoomEvent, DisconnectReason } from "livekit-client";

interface HangoutRoomProps {
    roomName: string;
    host: User;
    hangoutId: string;
}

// Inner component that uses LiveKit hooks
const HangoutRoomContent = ({
    roomName,
    host,
    hangoutId,
}: HangoutRoomProps) => {
    const router = useRouter();
    const participants = useParticipants();
    const { user } = useUser();
    const isRecording = useIsRecording();
    const room = useRoomContext();
    const [isEndingRoom, setIsEndingRoom] = useState(false);

    // Listen for hangout notifications via data channel
    useDataChannel('hangout-notifications', (message) => {
        try {
            const data = JSON.parse(new TextDecoder().decode(message.payload));
            console.log('[HANGOUT_NOTIFICATION]', data);

            if (data.type === 'ROOM_CLOSED_BY_HOST') {
                // Only show notification to non-host participants
                if (!isHost) {
                    toast.info('Room closed by the host', {
                        duration: 3000,
                    });

                    // Redirect to hangouts page after a short delay
                    setTimeout(() => {
                        router.push('/hangouts');
                    }, 2000);
                }
            }
        } catch (error) {
            console.error('[HANGOUT_NOTIFICATION_ERROR]', error);
        }
    });

    // Check if current user is the host
    const isHost = user?.id === host.externalUserId;

    // Handle room disconnection events
    useEffect(() => {
        if (!room) return;

        const handleDisconnected = (reason?: DisconnectReason) => {
            console.log(`[HANGOUT_DISCONNECT] Room disconnected: ${reason}`);

            // Show appropriate message based on disconnect reason
            if (reason === DisconnectReason.ROOM_DELETED) {
                if (!isHost) {
                    toast.info('Room was ended by the host');
                }
            } else if (reason === DisconnectReason.PARTICIPANT_REMOVED) {
                toast.info('You were removed from the hangout');
            } else if (reason && reason !== DisconnectReason.CLIENT_INITIATED) {
                toast.error('Connection lost');
            }

            // Redirect to hangouts page when disconnected
            router.push('/hangouts');
        };

        const handleReconnecting = () => {
            console.log('[HANGOUT_RECONNECTING] Attempting to reconnect...');
        };

        const handleReconnected = () => {
            console.log('[HANGOUT_RECONNECTED] Successfully reconnected');
            toast.success('Reconnected to hangout');
        };

        room.on(RoomEvent.Disconnected, handleDisconnected);
        room.on(RoomEvent.Reconnecting, handleReconnecting);
        room.on(RoomEvent.Reconnected, handleReconnected);

        return () => {
            room.off(RoomEvent.Disconnected, handleDisconnected);
            room.off(RoomEvent.Reconnecting, handleReconnecting);
            room.off(RoomEvent.Reconnected, handleReconnected);
        };
    }, [room, router, isHost]);

    // Handle browser close/refresh - cleanup participant status
    useEffect(() => {
        const handleBeforeUnload = () => {
            // Mark participant as left when browser is closed/refreshed
            if (user?.id) {
                navigator.sendBeacon('/api/hangout/leave', JSON.stringify({
                    hangoutId,
                    userId: user.id
                }));
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);
        return () => window.removeEventListener('beforeunload', handleBeforeUnload);
    }, [hangoutId, user?.id]);

    const handleLeave = useCallback(async () => {
        try {
            // Mark participant as left in the database
            await fetch('/api/hangout/leave', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ hangoutId }),
            });
        } catch (error) {
            console.error('Failed to update leave status:', error);
            // Continue with navigation even if API call fails
        }

        // Disconnect from the room first, then navigate
        if (room) {
            try {
                await room.disconnect();
                console.log('[HANGOUT_LEAVE] Successfully disconnected from room');
            } catch (disconnectError) {
                console.error('[HANGOUT_LEAVE] Error disconnecting from room:', disconnectError);
            }
        }

        // Navigate to hangouts page
        router.push('/hangouts');
    }, [router, hangoutId, room]);

    const handleEndRoom = useCallback(async () => {
        if (!isHost) return;

        setIsEndingRoom(true);
        try {
            // Send notification to participants before ending the room
            if (room) {
                try {
                    const roomClosureMessage = JSON.stringify({
                        type: 'ROOM_CLOSED_BY_HOST',
                        message: 'Room closed by the host',
                        timestamp: new Date().toISOString(),
                    });

                    await room.localParticipant.publishData(
                        new TextEncoder().encode(roomClosureMessage),
                        { topic: 'hangout-notifications' }
                    );

                    console.log('[HANGOUT_END] Sent room closure notification to participants');
                } catch (notificationError) {
                    console.error('[HANGOUT_END] Failed to send notification:', notificationError);
                }
            }

            // End the hangout room on the server
            const response = await fetch('/api/hangout/end', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ hangoutId }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to end hangout');
            }

            toast.success('Hangout ended successfully');

            // Disconnect from the room after a short delay to allow notification delivery
            setTimeout(() => {
                if (room) {
                    room.disconnect();
                }
                router.push('/hangouts');
            }, 1000);

        } catch (error) {
            console.error('Failed to end hangout:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to end hangout');
        } finally {
            setIsEndingRoom(false);
        }
    }, [isHost, hangoutId, router, room]);

    return (
        <div className="flex flex-col h-screen bg-black text-white">
            {/* Header - Simplified, just room info */}
            <div className="flex items-center justify-center p-4 bg-gray-900/50 backdrop-blur shrink-0">
                <div className="flex items-center space-x-3">
                    <Avatar className="w-8 h-8">
                        <AvatarImage src={host.imageURL} />
                        <AvatarFallback>{host.username.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className="text-center">
                        <h1 className="font-semibold">{roomName}</h1>
                        <p className="text-sm text-gray-400">
                            {participants.length} people here
                            {isRecording && (
                                <span className="ml-2 inline-flex items-center space-x-1 text-red-400">
                                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                                    <span className="text-xs">Recording</span>
                                </span>
                            )}
                        </p>
                    </div>
                </div>
            </div>

            {/* Main video grid - Takes remaining space */}
            <div className="flex-1 min-h-0 overflow-hidden">
                <HangoutGridLayout />
            </div>

            {/* Bottom Controls - All controls moved here */}
            <div className="flex flex-col space-y-4 p-4 bg-gray-900/50 backdrop-blur shrink-0">
                {/* Media Controls */}
                <div className="flex items-center justify-center">
                    <ControlBar
                        variation="minimal"
                        controls={{
                            microphone: true,
                            camera: true,
                            screenShare: isHost, // Only host can screen share
                            leave: false, // We handle leave separately
                        }}
                    />
                </div>

                {/* Action Controls */}
                <div className="flex items-center justify-center space-x-3">
                    {/* Recording Controls - Only for host */}
                    {isHost && (
                        <HangoutRecordingControls
                            hangoutId={hangoutId}
                            hostUserId={host.externalUserId}
                        />
                    )}

                    {/* End Room Button - Only for host */}
                    {isHost && (
                        <Button
                            variant="destructive"
                            size="sm"
                            onClick={handleEndRoom}
                            disabled={isEndingRoom}
                            className="flex items-center space-x-2"
                        >
                            <X className="h-4 w-4" />
                            <span>{isEndingRoom ? 'Ending...' : 'End Room'}</span>
                        </Button>
                    )}

                    {/* Leave Button - For all participants */}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleLeave}
                        className="text-white hover:bg-gray-800 flex items-center space-x-2"
                    >
                        <LogOut className="h-4 w-4" />
                        <span>Leave</span>
                    </Button>
                </div>
            </div>

            {/* Audio renderer for all participants */}
            <RoomAudioRenderer />
        </div>
    );
};

// Main component wrapper with LiveKit room
export const HangoutRoom = ({
    roomName,
    host,
    hangoutId,
}: HangoutRoomProps) => {
    const { token, name, identity } = useHangoutToken(hangoutId);

    if (!token || !name || !identity) {
        return (
            <div className="flex items-center justify-center h-screen bg-black text-white">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
                    <p>Connecting to hangout...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="h-screen w-full" data-lk-theme="default">
            <LiveKitRoom
                token={token}
                serverUrl={process.env.NEXT_PUBLIC_LIVEKIT_WS_URL!}
                connect={true}
                audio={true}
                video={true}
                className="h-full w-full"
            >
                <HangoutRoomContent
                    roomName={roomName}
                    host={host}
                    hangoutId={hangoutId}
                />
            </LiveKitRoom>
        </div>
    );
};