"use client";

import { Stream, User } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Video, Users, Clock, UserPlus } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";

interface LivestreamWithUser extends Stream {
  user: {
    id: string;
    username: string;
    imageURL: string | null;
  };
}

interface LivestreamLandingProps {
  activeStreams: LivestreamWithUser[];
  currentUser?: User | null;
}

export const LivestreamLanding = ({
  activeStreams,
  currentUser,
}: LivestreamLandingProps) => {
  const hasActiveStreams = activeStreams.length > 0;
  const [roomName, setRoomName] = useState("");
  const router = useRouter();

  const handleJoinRoom = (e: React.FormEvent) => {
    e.preventDefault();
    if (roomName.trim()) {
      router.push(`/rooms/${roomName.trim()}`);
    }
  };

  if (!hasActiveStreams) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
        <div className="text-center space-y-6 max-w-md">
          <div className="w-20 h-20 mx-auto bg-muted rounded-full flex items-center justify-center">
            <Video className="w-10 h-10 text-baseuted-foreground" />
          </div>

          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-foreground">
              No livestreams active
            </h2>
            <p className="text-baseuted-foreground">
              Be the first to go live and connect with your audience
            </p>
          </div>

          {currentUser ? (
            <Link href={`/u/${currentUser.username}`}>
              <Button
                size="lg"
                className="bg-red-600 hover:bg-red-700 text-white px-8 py-3"
              >
                <Video className="w-5 h-5 mr-2" />
                Start Streaming
              </Button>
            </Link>
          ) : (
            <Link href="/sign-in">
              <Button
                size="lg"
                className="bg-red-600 hover:bg-red-700 text-white px-8 py-3"
              >
                <Video className="w-5 h-5 mr-2" />
                Sign in to Stream
              </Button>
            </Link>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Join Room Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-base font-semibold text-card-foreground flex items-center">
              <UserPlus className="w-5 h-5 mr-2" />
              Join a Room
            </h3>
            <p className="text-baseuted-foreground text-basem">
              Enter a room name to join a video conference
            </p>
          </div>
        </div>
        <form onSubmit={handleJoinRoom} className="flex space-x-3">
          <Input
            type="text"
            placeholder="Enter room name or user ID..."
            value={roomName}
            onChange={(e) => setRoomName(e.target.value)}
            className="flex-1"
          />
          <Button
            type="submit"
            disabled={!roomName.trim()}
            className="bg-primary hover:bg-primary/90 text-primary-foreground"
          >
            Join Room
          </Button>
        </form>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Live Now</h2>
          <p className="text-baseuted-foreground">
            {activeStreams.length} stream{activeStreams.length === 1 ? "" : "s"}{" "}
            currently live
          </p>
        </div>

        {currentUser && (
          <Link href={`/u/${currentUser.username}`}>
            <Button className="bg-destructive hover:bg-destructive/90 text-destructive-foreground">
              <Video className="w-4 h-4 mr-2" />
              Go Live
            </Button>
          </Link>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {activeStreams.map((stream) => (
          <Link key={stream.id} href={`/${stream.user.username}`}>
            <div className="bg-card border border-border rounded-lg hover:bg-accent/10 transition-colors cursor-pointer p-4">
              <div className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                      {stream.user.imageURL ? (
                        <Image
                          src={stream.user.imageURL}
                          alt={stream.user.username}
                          width={40}
                          height={40}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-basem font-medium text-card-foreground">
                          {stream.user.username.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                    <div>
                      <h3 className="text-basem font-semibold text-card-foreground">
                        {stream.name}
                      </h3>
                      <p className="text-xs text-baseuted-foreground">
                        @{stream.user.username}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-destructive rounded-full animate-pulse"></div>
                    <span className="text-xs font-medium text-destructive">
                      LIVE
                    </span>
                  </div>
                </div>
              </div>

              <div className="pt-0">
                <div className="aspect-video bg-muted rounded-lg flex items-center justify-center mb-3">
                  <Video className="w-8 h-8 text-baseuted-foreground" />
                </div>

                <div className="flex items-center justify-between text-xs text-baseuted-foreground">
                  <div className="flex items-center space-x-1">
                    <Users className="w-3 h-3" />
                    <span>Live viewers</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>Live</span>
                  </div>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};
