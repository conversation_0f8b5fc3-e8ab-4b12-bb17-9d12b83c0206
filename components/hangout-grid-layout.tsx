"use client";

import { useTracks, ParticipantTile, TrackReference } from "@livekit/components-react";
import { Track } from "livekit-client";
import { cn } from "@/lib/utils";

interface HangoutGridLayoutProps {
  className?: string;
}

// Custom participant tile with proper 9:16 aspect ratio constraints
const HangoutParticipantTile = ({ trackRef }: { trackRef: TrackReference }) => {
  return (
    <div className="relative bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
      {/* Fixed 9:16 aspect ratio container that fits the available height */}
      <div className="w-full h-full flex items-center justify-center">
        <div className="h-full aspect-[9/16] relative max-w-full">
          <ParticipantTile
            trackRef={trackRef}
            className="w-full h-full"
            style={{
              objectFit: 'cover',
              objectPosition: 'center'
            }}
          />
        </div>
      </div>
    </div>
  );
};

export const HangoutGridLayout = ({ className }: HangoutGridLayoutProps) => {
  // Get all video tracks for the grid
  const tracks = useTracks([
    Track.Source.Camera,
    Track.Source.ScreenShare,
  ]);

  // Create a grid layout based on participant count
  const getGridClass = (count: number) => {
    if (count === 1) return "grid-cols-1";
    if (count === 2) return "grid-cols-2";
    if (count <= 4) return "grid-cols-2";
    if (count <= 6) return "grid-cols-3";
    return "grid-cols-3";
  };

  // Calculate grid rows based on participant count
  const getGridRows = (count: number) => {
    if (count === 1) return "grid-rows-1";
    if (count === 2) return "grid-rows-1";
    if (count <= 4) return "grid-rows-2";
    if (count <= 6) return "grid-rows-2";
    return "grid-rows-3";
  };

  return (
    <div className={cn("h-full p-4 overflow-hidden", className)}>
      <div className={cn(
        "grid gap-4 h-full w-full place-items-center",
        getGridClass(tracks.length),
        getGridRows(tracks.length)
      )}>
        {tracks.map((trackRef) => (
          <div
            key={`${trackRef.participant.identity}-${trackRef.source}`}
            className="w-full h-full flex items-center justify-center"
          >
            <HangoutParticipantTile trackRef={trackRef} />
          </div>
        ))}
      </div>
    </div>
  );
};
