"use client";

import { useProfileCompletion } from "@/hooks/use-profile-completion";
import { Loader2 } from "lucide-react";

interface ProfileCompletionWrapperProps {
    children: React.ReactNode;
    requiresAuth?: boolean;
}

export function ProfileCompletionWrapper({
    children,
    requiresAuth = true
}: ProfileCompletionWrapperProps) {
    const { isLoading } = useProfileCompletion();

    // If auth is not required, render children directly
    if (!requiresAuth) {
        return <>{children}</>;
    }

    // Show loading state while checking profile
    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <Loader2 className="h-8 w-8 animate-spin text-white" />
            </div>
        );
    }

    // If we reach here, either profile is complete or user will be redirected
    // The redirect happens in the hook, so we can render children
    return <>{children}</>;
} 