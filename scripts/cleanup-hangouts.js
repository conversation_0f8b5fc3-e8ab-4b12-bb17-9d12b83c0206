#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

const db = new PrismaClient();

/**
 * Cleanup script to reset all hangout rooms to inactive status
 * This is useful for development when restarting the server
 */
async function cleanupHangouts() {
  try {
    console.log("🧹 Starting hangout cleanup...");

    // Update all active hangout rooms to ended status
    const updatedRooms = await db.hangoutRoom.updateMany({
      where: {
        status: 'ACTIVE',
      },
      data: {
        status: 'ENDED',
        endedAt: new Date(),
      },
    });

    console.log(`📊 Updated ${updatedRooms.count} hangout rooms to ENDED status`);

    // Update all joined participants to left status
    const updatedParticipants = await db.hangoutParticipant.updateMany({
      where: {
        status: 'JOINED',
      },
      data: {
        status: 'LEFT',
        leftAt: new Date(),
      },
    });

    console.log(`👥 Updated ${updatedParticipants.count} participants to LEFT status`);

    console.log("✅ Hangout cleanup completed successfully!");
  } catch (error) {
    console.error("❌ Error during hangout cleanup:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  cleanupHangouts();
}

module.exports = { cleanupHangouts };
