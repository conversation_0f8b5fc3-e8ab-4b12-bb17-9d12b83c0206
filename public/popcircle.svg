<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="67" height="67" viewBox="0 0 67 67">
  <defs>
    <filter id="Ellipse_1">
      <feOffset dy="20" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="10" result="blur"/>
      <feFlood flood-opacity="0.161" result="color"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur"/>
      <feComposite operator="in" in="color"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
  </defs>
  <g id="popcircle" transform="translate(-4100 -737)">
    <g data-type="innerShadowGroup">
      <circle id="Ellipse_1-2" data-name="Ellipse 1" cx="33.5" cy="33.5" r="33.5" transform="translate(4100 737)" fill="#ff0800"/>
      <g transform="matrix(1, 0, 0, 1, 4100, 737)" filter="url(#Ellipse_1)">
        <circle id="Ellipse_1-3" data-name="Ellipse 1" cx="33.5" cy="33.5" r="33.5" fill="#fff"/>
      </g>
    </g>
    <text id="pop" transform="translate(4106 778)" fill="#fff" font-size="29" font-family="Quicksand-Bold, Quicksand" font-weight="700" letter-spacing="0.03em"><tspan x="0" y="0">pop</tspan></text>
  </g>
</svg>
