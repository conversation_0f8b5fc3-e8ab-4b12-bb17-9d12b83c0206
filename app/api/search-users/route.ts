import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function GET(req: NextRequest) {
  const term = req.nextUrl.searchParams.get("term")?.toLowerCase() || "";

  if (!term) {
    return NextResponse.json([]);
  }

  try {
    const users = await db.user.findMany({
      where: {
        username: {
          startsWith: term,
          mode: "insensitive",
        },
      },
      select: {
        username: true,
        imageURL: true,
      },
      take: 5,
    });

    console.log("Matched users:", users);
    return NextResponse.json(users); // should be array of { username, imageURL }
  } catch (error) {
    console.error("Search error:", error);
    return NextResponse.json([], { status: 500 });
  }
}
