import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { getUserById } from "@/lib/user-service";

export async function POST(req: NextRequest) {
    try {
        const { hostIdentity, isLive } = await req.json();

        if (!hostIdentity || typeof isLive !== 'boolean') {
            return NextResponse.json({ error: "Host identity and isLive status are required" }, { status: 400 });
        }

        // Get authenticated user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get host information
        const host = await getUserById(hostIdentity);
        if (!host || host.externalUserId !== userId) {
            return NextResponse.json({ error: "Unauthorized or host not found" }, { status: 403 });
        }

        // Update the stream status
        const userStream = await db.stream.findFirst({
            where: { userId: host.id }
        });

        if (userStream) {
            await db.stream.update({
                where: { id: userStream.id },
                data: { isLive }
            });
            console.log(`[STREAM_STATUS] Stream ${isLive ? 'started' : 'ended'} for host:`, host.username);
        }

        return NextResponse.json({ success: true });

    } catch (error) {
        console.error("Failed to update stream status:", error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : "Failed to update stream status" },
            { status: 500 }
        );
    }
}
