import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { clerkClient } from "@clerk/clerk-sdk-node";
import { db } from "@/lib/db";
import path from "path";
import { writeFile, mkdir } from "fs/promises";

export async function POST(req: Request) {
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await req.formData();

  const username = formData.get("username") as string;
  const name = formData.get("name") as string;
  const birthday = formData.get("birthday") as string;
  const location = formData.get("location") as string;
  const gender = formData.get("gender") as string;
  const bio = formData.get("bio") as string | null;
  const interestsRaw = formData.get("interests") as string | null;

  const interests: string[] = interestsRaw ? JSON.parse(interestsRaw) : [];

  const photo = formData.get("photo") as File;
  let photoUrl = "/uploads/default.jpg";

  if (photo) {
    const bytes = await photo.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const filename = `${Date.now()}-${photo.name.replace(/\s/g, "_")}`;
    const uploadPath = path.join(process.cwd(), "public", "uploads", filename);
    await mkdir(path.dirname(uploadPath), { recursive: true });
    await writeFile(uploadPath, buffer);
    photoUrl = `/uploads/${filename}`;
  }

  try {
    await db.user.upsert({
      where: { externalUserId: userId },
      create: {
        externalUserId: userId,
        username,
        name,
        birthday: new Date(birthday),
        location,
        gender,
        bio,
        interests: { set: interests },
        imageURL: photoUrl,
        profileComplete: true,
      },
      update: {
        username,
        name,
        birthday: new Date(birthday),
        location,
        gender,
        bio,
        interests: { set: interests },
        imageURL: photoUrl,
        profileComplete: true,
      },
    });

    await clerkClient.users.updateUser(userId, {
      username,
      firstName: name.split(" ")[0],
      lastName: name.split(" ")[1] || "",
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Profile update error:", error);
    return NextResponse.json(
      { error: "Failed to update profile" },
      { status: 500 }
    );
  }
}
