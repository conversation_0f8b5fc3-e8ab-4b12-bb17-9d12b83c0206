import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { clerkClient } from "@clerk/clerk-sdk-node";
import { db } from "@/lib/db";
import path from "path";
import { writeFile, mkdir } from "fs/promises";

export async function POST(req: Request) {
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await req.formData();

  const rawUsername = formData.get("username") as string;
  const name = formData.get("name") as string;
  const birthday = formData.get("birthday") as string;
  const location = formData.get("location") as string;
  const gender = formData.get("gender") as string;
  const bio = formData.get("bio") as string | null;
  const interestsRaw = formData.get("interests") as string | null;

  const interests: string[] = interestsRaw ? JSON.parse(interestsRaw) : [];

  // Sanitize username for Clerk API - remove invalid characters and ensure it meets requirements
  const username = rawUsername
    .toLowerCase()
    .replace(/[^a-z0-9_]/g, '') // Remove invalid characters
    .substring(0, 30); // Limit length

  // Validate required fields
  if (!username || !name || !birthday || !location || !gender) {
    return NextResponse.json(
      { error: "All fields are required" },
      { status: 400 }
    );
  }

  // Validate username length
  if (username.length < 3) {
    return NextResponse.json(
      { error: "Username must be at least 3 characters long" },
      { status: 400 }
    );
  }

  const photo = formData.get("photo") as File;
  let photoUrl = "/uploads/default.jpg";

  if (photo) {
    const bytes = await photo.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const filename = `${Date.now()}-${photo.name.replace(/\s/g, "_")}`;
    const uploadPath = path.join(process.cwd(), "public", "uploads", filename);
    await mkdir(path.dirname(uploadPath), { recursive: true });
    await writeFile(uploadPath, buffer);
    photoUrl = `/uploads/${filename}`;
  }

  try {
    await db.user.upsert({
      where: { externalUserId: userId },
      create: {
        externalUserId: userId,
        username,
        name,
        birthday: new Date(birthday),
        location,
        gender,
        bio,
        interests: { set: interests },
        imageURL: photoUrl,
        profileComplete: true,
      },
      update: {
        username,
        name,
        birthday: new Date(birthday),
        location,
        gender,
        bio,
        interests: { set: interests },
        imageURL: photoUrl,
        profileComplete: true,
      },
    });

    // Update Clerk user with better error handling
    try {
      await clerkClient.users.updateUser(userId, {
        username,
        firstName: name.split(" ")[0],
        lastName: name.split(" ")[1] || "",
      });
    } catch (clerkError: any) {
      console.error("Clerk API error:", clerkError);

      // Handle specific Clerk errors
      if (clerkError?.errors?.[0]?.code === "form_identifier_exists") {
        return NextResponse.json(
          { error: "Username is already taken. Please choose a different one." },
          { status: 400 }
        );
      }

      if (clerkError?.errors?.[0]?.code === "form_param_format_invalid") {
        return NextResponse.json(
          { error: "Username contains invalid characters. Please use only letters, numbers, and underscores." },
          { status: 400 }
        );
      }

      // Generic Clerk error
      return NextResponse.json(
        { error: "Failed to update user information. Please try again." },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Profile update error:", error);
    return NextResponse.json(
      { error: "Failed to update profile" },
      { status: 500 }
    );
  }
}
