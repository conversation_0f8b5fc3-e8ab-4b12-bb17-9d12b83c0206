import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { EgressClient } from "livekit-server-sdk";

const egressClient = new EgressClient(
    process.env.LIVEKIT_API_URL!,
    process.env.LIVEKIT_API_KEY!,
    process.env.LIVEKIT_API_SECRET!
);

export async function GET(req: NextRequest) {
    try {
        const { searchParams } = new URL(req.url);
        const egressId = searchParams.get('egressId');
        const sessionId = searchParams.get('sessionId');

        if (!egressId || !sessionId) {
            return NextResponse.json({ error: "Egress ID and session ID are required" }, { status: 400 });
        }

        // Get authenticated user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get the recording session
        const recordedSession = await db.recordedSession.findUnique({
            where: { id: sessionId },
            include: { host: true }
        });

        if (!recordedSession) {
            return NextResponse.json({ error: "Recording session not found" }, { status: 404 });
        }

        // Verify user is the host
        if (recordedSession.host.externalUserId !== userId) {
            return NextResponse.json({ error: "Unauthorized - not the host" }, { status: 403 });
        }

        // Get egress status from LiveKit
        const egressInfo = await egressClient.listEgress({ egressId });
        const egress = egressInfo.length > 0 ? egressInfo[0] : null;

        if (!egress) {
            return NextResponse.json({ error: "Egress not found" }, { status: 404 });
        }

        // Update database based on egress status
        const updateData: {
            processingStatus?: 'READY' | 'FAILED' | 'PUBLISHED';
            recordingEndedAt?: Date;
            duration?: number;
            isPublished?: boolean;
            publishedAt?: Date;
        } = {};

        console.log(`[RECORDING_STATUS_DEBUG] Egress status: ${egress.status}`);
        console.log(`[RECORDING_STATUS_DEBUG] Current session status: ${recordedSession.processingStatus}`);

        if (egress.status.toString() === 'EGRESS_COMPLETE' && recordedSession.processingStatus === 'PROCESSING') {
            updateData.processingStatus = 'PUBLISHED';
            updateData.recordingEndedAt = new Date();
            updateData.isPublished = true;
            updateData.publishedAt = new Date();

            // Calculate duration if we have start and end times
            if (egress.startedAt && egress.endedAt) {
                const startTime = new Date(Number(egress.startedAt) / 1000000); // Convert nanoseconds to milliseconds
                const endTime = new Date(Number(egress.endedAt) / 1000000);
                updateData.duration = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
            }

            console.log(`[RECORDING_STATUS_DEBUG] Marking session as published:`, updateData);
        } else if (egress.status.toString() === 'EGRESS_FAILED') {
            updateData.processingStatus = 'FAILED';
            console.log(`[RECORDING_STATUS_DEBUG] Marking session as failed`);
        }

        // Update database if needed
        let updatedSession = recordedSession;
        if (Object.keys(updateData).length > 0) {
            updatedSession = await db.recordedSession.update({
                where: { id: sessionId },
                data: updateData,
                include: { host: true }
            });
        }

        return NextResponse.json({
            success: true,
            egressId: egress.egressId,
            sessionId: updatedSession.id,
            status: egress.status,
            processingStatus: updatedSession.processingStatus,
            duration: updatedSession.duration,
            recordingStartedAt: updatedSession.recordingStartedAt,
            recordingEndedAt: updatedSession.recordingEndedAt,
            isPublished: updatedSession.isPublished,
            publishedAt: updatedSession.publishedAt,
            error: egress.error
        });

    } catch (error) {
        console.error("Failed to get recording status:", error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : "Failed to get recording status" },
            { status: 500 }
        );
    }
} 