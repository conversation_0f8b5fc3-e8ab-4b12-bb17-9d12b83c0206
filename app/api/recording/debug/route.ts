import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

export async function GET() {
    try {
        // Get authenticated user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get all recorded sessions for debugging
        const allSessions = await db.recordedSession.findMany({
            include: {
                host: {
                    select: {
                        id: true,
                        username: true,
                        imageURL: true,
                        externalUserId: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });

        console.log(`[DEBUG_API] Found ${allSessions.length} total recorded sessions`);

        const sessionData = allSessions.map(session => ({
            id: session.id,
            title: session.title,
            egressId: session.egressId,
            processingStatus: session.processingStatus,
            isPublished: session.isPublished,
            publishedAt: session.publishedAt,
            videoUrl: session.videoUrl,
            hlsUrl: session.hlsUrl,
            duration: session.duration,
            recordingStartedAt: session.recordingStartedAt,
            recordingEndedAt: session.recordingEndedAt,
            hostUsername: session.host.username,
            hostExternalUserId: session.host.externalUserId,
            r2Path: session.videoUrl ? `${process.env.CLOUDFLARE_R2_BUCKET}/${session.videoUrl}` : null,
        }));

        return NextResponse.json({
            success: true,
            totalSessions: allSessions.length,
            publishedSessions: allSessions.filter(s => s.isPublished).length,
            r2Config: {
                endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
                bucket: process.env.CLOUDFLARE_R2_BUCKET,
                hasAccessKey: !!process.env.CLOUDFLARE_R2_ACCESS_KEY,
                hasSecretKey: !!process.env.CLOUDFLARE_R2_SECRET_KEY,
            },
            sessions: sessionData,
        });

    } catch (error) {
        console.error("Debug API failed:", error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : "Debug API failed" },
            { status: 500 }
        );
    }
} 