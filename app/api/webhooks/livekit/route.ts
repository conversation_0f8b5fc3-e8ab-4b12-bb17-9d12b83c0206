import { db } from "@/lib/db";
import { WebhookReceiver, WebhookEvent } from "livekit-server-sdk";
import { headers } from "next/headers";

const receiver = new WebhookReceiver(
  process.env.LIVEKIT_API_KEY!,
  process.env.LIVEKIT_API_SECRET!
);

export async function POST(req: Request) {
  const body = await req.text();
  const headerPayload = await headers();
  const authorization = headerPayload.get("Authorization");

  if (!authorization) {
    return new Response("No authorization header", { status: 400 });
  }

  try {
    const event = (await receiver.receive(body, authorization)) as WebhookEvent;
    console.log("Webhook Event Received:", event);

    // Handle traditional ingress events
    if (event.event === "ingress_started") {
      console.log("Ingress Started Event Detected.");

      if (event.ingressInfo?.ingressId) {
        await db.stream.update({
          where: { ingressId: event.ingressInfo.ingressId },
          data: { isLive: true },
        });
      }
    }

    if (event.event === "ingress_ended") {
      console.log("Ingress Ended Event Detected.");

      if (event.ingressInfo?.ingressId) {
        await db.stream.update({
          where: { ingressId: event.ingressInfo.ingressId },
          data: { isLive: false },
        });
      }
    }

    // Handle room events for video conferencing
    if (event.event === "room_started") {
      console.log("Room Started Event Detected:", event.room?.name);

      if (event.room?.name) {
        try {
          // Room name is the user ID in our system
          const user = await db.user.findUnique({
            where: { id: event.room.name },
            include: { streams: { take: 1, orderBy: { createdAt: 'desc' } } }
          });

          if (user && user.streams[0]) {
            await db.stream.update({
              where: { id: user.streams[0].id },
              data: { isLive: true },
            });
            console.log("Stream marked as live for room:", event.room.name);
          } else {
            console.warn("User or stream not found for room:", event.room.name);
          }
        } catch (error) {
          console.error("Error handling room_started event:", error);
        }
      }
    }

    if (event.event === "room_finished") {
      console.log("Room Finished Event Detected:", event.room?.name);

      if (event.room?.name) {
        try {
          // Room name is the user ID in our system
          const user = await db.user.findUnique({
            where: { id: event.room.name },
            include: { streams: { take: 1, orderBy: { createdAt: 'desc' } } }
          });

          if (user && user.streams[0]) {
            await db.stream.update({
              where: { id: user.streams[0].id },
              data: { isLive: false },
            });
            console.log("Stream marked as offline for room:", event.room.name);
          } else {
            console.warn("User or stream not found for room:", event.room.name);
          }
        } catch (error) {
          console.error("Error handling room_finished event:", error);
        }
      }
    }

    return new Response("Webhook received successfully", { status: 200 });
  } catch (error) {
    console.error("Error handling webhook event:", error);
    return new Response("Error processing webhook", { status: 500 });
  }
}
