import { Webhook } from "svix";
import { headers } from "next/headers";
import { WebhookEvent } from "@clerk/nextjs/server";
import { db } from "@/lib/db";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function POST(req: Request) {
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;
  if (!WEBHOOK_SECRET) throw new Error("Missing Clerk Webhook Secret");

  const headerPayload = await headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Missing Svix headers", { status: 400 });
  }

  const payload = await req.json();
  const body = JSON.stringify(payload);
  const wh = new Webhook(WEBHOOK_SECRET);

  let evt: WebhookEvent;
  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error("Webhook verification failed:", err);
    return new Response("Verification failed", { status: 400 });
  }

  const { id, image_url } = payload.data;
  const eventType = evt.type;

  try {
    if (eventType === "user.created") {
      await db.user.create({
        data: {
          externalUserId: id,
          username: `user-${Date.now()}`, // fallback username
          imageURL: image_url,
        },
      });
    }

    if (eventType === "user.updated") {
      await db.user.update({
        where: { externalUserId: id },
        data: { imageURL: image_url },
      });
    }

    if (eventType === "user.deleted") {
      await db.user.delete({
        where: { externalUserId: id },
      });
    }

    return new Response("Webhook processed", { status: 200 });
  } catch (error) {
    console.error("Database error:", error);
    return new Response("Internal server error", { status: 500 });
  }
}
