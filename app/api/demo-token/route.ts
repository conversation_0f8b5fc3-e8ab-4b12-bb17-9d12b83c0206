import { NextRequest, NextResponse } from 'next/server';
import { AccessToken, VideoGrant } from 'livekit-server-sdk';
import { randomString } from '@/lib/client-utils';

const API_KEY = process.env.LIVEKIT_API_KEY;
const API_SECRET = process.env.LIVEKIT_API_SECRET;

export async function POST(request: NextRequest) {
  try {
    const { roomName, participantName } = await request.json();

    if (!roomName || !participantName) {
      return new NextResponse('Missing roomName or participantName', { status: 400 });
    }

    // Create a demo token with full permissions (like Meet)
    const token = new AccessToken(API_KEY, API_SECRET, {
      identity: `${participantName}__${randomString(4)}`,
      name: participantName,
    });

    token.ttl = '5m';
    
    const grant: VideoGrant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
    };
    
    token.addGrant(grant);
    const jwt = await token.toJwt();

    return NextResponse.json({
      token: jwt,
      identity: `${participantName}__${randomString(4)}`,
      name: participantName,
    });
  } catch (error) {
    console.error('Demo token error:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}
