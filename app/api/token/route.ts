import { NextRequest, NextResponse } from "next/server";
import { AccessToken } from "livekit-server-sdk";
import { v4 } from "uuid";
import { db } from "@/lib/db";
import { StreamRole } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";

// Role-based permissions mapping
const getRolePermissions = (role: StreamRole, isHost: boolean) => {
  switch (role) {
    case StreamRole.HOST:
      return {
        canPublish: true,
        canPublishData: true,
        canPublishScreen: true,
        canSubscribe: true,
        canUpdateMetadata: true,
      };
    case StreamRole.CO_HOST:
      return {
        canPublish: true,
        canPublishData: true,
        canPublishScreen: true,
        canSubscribe: true,
        canUpdateMetadata: false,
      };
    case StreamRole.GUEST:
      return {
        canPublish: true,
        canPublishData: true,
        canPublishScreen: false,
        canSubscribe: true,
        canUpdateMetadata: false,
      };
    case StreamRole.VIEWER:
    default:
      return {
        canPublish: isHost,
        canPublishData: true,
        canPublishScreen: false,
        canSubscribe: true,
        canUpdateMetadata: false,
      };
  }
};

export async function POST(req: NextRequest) {
  try {
    const { hostUsername } = await req.json();

    if (!hostUsername) {
      return NextResponse.json(
        { error: "Host username is required" },
        { status: 400 }
      );
    }

    console.log("Creating token for host username:", hostUsername);

    // Get host info from DB by username
    const host = await db.user.findUnique({
      where: { username: hostUsername },
    });
    if (!host) {
      return NextResponse.json({ error: "Host not found" }, { status: 404 });
    }

    let currentUser;
    let participantId;
    let participantName;
    let isHost = false;

    try {
      const { userId } = await auth();
      if (userId && host.externalUserId === userId) {
        currentUser = host;
        participantId = host.id;
        participantName = host.username;
        isHost = true;
        console.log("Authenticated host detected:", {
          username: host.username,
          isHost,
        });
      }
    } catch (authError) {
      console.log("No authenticated user, creating guest:", authError);
    }

    // Guest fallback
    if (!currentUser) {
      participantId = v4();
      participantName = `guest#${Math.floor(Math.random() * 1000)}`;
      console.log("Generated guest user:", { participantId, participantName });
    }

    const role = isHost ? StreamRole.HOST : StreamRole.VIEWER;
    const permissions = getRolePermissions(role, isHost);

    const token = new AccessToken(
      process.env.LIVEKIT_API_KEY!,
      process.env.LIVEKIT_API_SECRET!,
      {
        identity: participantId,
        name: participantName,
        metadata: JSON.stringify({ role, permissions, isHost }),
      }
    );

    token.addGrant({
      room: "global-dashboard-room",

      roomJoin: true,
      canPublish: permissions.canPublish,
      canPublishData: permissions.canPublishData,
      canSubscribe: permissions.canSubscribe,
      hidden: !isHost && role === StreamRole.VIEWER,
    });

    const jwt = await token.toJwt();
    console.log("Token created successfully for:", {
      participantName,
      role,
      isHost,
    });

    return NextResponse.json({
      token: jwt,
      identity: participantId,
      name: participantName,
    });
  } catch (error) {
    console.error("Token creation failed:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to create token",
      },
      { status: 500 }
    );
  }
}
