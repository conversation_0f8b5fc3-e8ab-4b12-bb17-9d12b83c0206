import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { getUserById } from "@/lib/user-service";

export async function POST(req: NextRequest) {
    try {
        const { hostIdentity, roomName } = await req.json();

        if (!hostIdentity && !roomName) {
            return NextResponse.json({ error: "Host identity or room name is required" }, { status: 400 });
        }

        // Get authenticated user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get host information - use roomName if provided (for new room system)
        const hostId = roomName || hostIdentity;
        const host = await getUserById(hostId);
        if (!host || host.externalUserId !== userId) {
            return NextResponse.json({ error: "Unauthorized or host not found" }, { status: 403 });
        }

        // Mark the stream as not live
        const userStream = await db.stream.findFirst({
            where: { userId: host.id }
        });

        if (userStream) {
            await db.stream.update({
                where: { id: userStream.id },
                data: { isLive: false }
            });
            console.log("Stream marked as not live for host:", host.username);
        }

        return NextResponse.json({ success: true });

    } catch (error) {
        console.error("Failed to end stream:", error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : "Failed to end stream" },
            { status: 500 }
        );
    }
} 