import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";

export async function GET(request: NextRequest) {
    try {
        const { userId } = await auth();

        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const searchParams = request.nextUrl.searchParams;
        const requestedUserId = searchParams.get('userId');

        // Ensure the requested user ID matches the authenticated user
        if (requestedUserId !== userId) {
            return NextResponse.json({ error: "Forbidden" }, { status: 403 });
        }

        const user = await db.user.findUnique({
            where: { externalUserId: userId },
            select: {
                id: true,
                username: true,
                profileComplete: true,
                externalUserId: true,
            },
        });

        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        return NextResponse.json(user);
    } catch (error) {
        console.error("Error fetching user profile:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
} 