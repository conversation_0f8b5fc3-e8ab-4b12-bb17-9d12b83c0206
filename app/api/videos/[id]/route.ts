import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { AwsClient } from "aws4fetch";

const r2Client = new AwsClient({
    service: "s3",
    region: "auto",
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_KEY!,
});

const R2_URL = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL || `https://${process.env.CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`;

interface RouteParams {
    params: Promise<{ id: string }>;
}

export async function GET(req: NextRequest, { params }: RouteParams) {
    try {
        const { id } = await params;
        const { searchParams } = new URL(req.url);
        const format = searchParams.get('format') || 'mp4'; // mp4 or hls

        if (!id) {
            return NextResponse.json({ error: "Video ID is required" }, { status: 400 });
        }

        // Get the recorded session
        const recordedSession = await db.recordedSession.findUnique({
            where: { id },
            include: {
                host: true,
                stream: true
            }
        });

        if (!recordedSession) {
            return NextResponse.json({ error: "Video not found" }, { status: 404 });
        }

        // Check if video is published or if user is the host
        const { userId } = await auth();
        const isHost = userId && recordedSession.host.externalUserId === userId;

        if (!recordedSession.isPublished && !isHost) {
            return NextResponse.json({ error: "Video not available" }, { status: 403 });
        }

        // Check if video is ready
        if (recordedSession.processingStatus !== 'READY' && recordedSession.processingStatus !== 'PUBLISHED') {
            return NextResponse.json({
                error: "Video is still processing",
                status: recordedSession.processingStatus
            }, { status: 202 });
        }

        // Generate presigned URL based on format
        let videoUrl: string;
        const expiresIn = 3600; // 1 hour

        if (format === 'hls' && recordedSession.hlsUrl) {
            // Generate presigned URL for HLS playlist
            const signedRequest = await r2Client.sign(
                new Request(`${R2_URL}/${process.env.CLOUDFLARE_R2_BUCKET}/${recordedSession.hlsUrl}?X-Amz-Expires=${expiresIn}`),
                {
                    aws: { signQuery: true },
                }
            );
            videoUrl = signedRequest.url.toString();
        } else if (recordedSession.videoUrl) {
            // Generate presigned URL for MP4 file
            const signedRequest = await r2Client.sign(
                new Request(`${R2_URL}/${process.env.CLOUDFLARE_R2_BUCKET}/${recordedSession.videoUrl}?X-Amz-Expires=${expiresIn}`),
                {
                    aws: { signQuery: true },
                }
            );
            videoUrl = signedRequest.url.toString();
        } else {
            return NextResponse.json({ error: "Video file not found" }, { status: 404 });
        }

        return NextResponse.json({
            success: true,
            videoUrl,
            format,
            session: {
                id: recordedSession.id,
                title: recordedSession.title,
                description: recordedSession.description,
                duration: recordedSession.duration,
                thumbnailUrl: recordedSession.thumbnailUrl,
                host: {
                    username: recordedSession.host.username,
                    imageURL: recordedSession.host.imageURL,
                },
                recordingStartedAt: recordedSession.recordingStartedAt,
                participantCount: recordedSession.participantCount,
            },
            expiresAt: new Date(Date.now() + expiresIn * 1000).toISOString(),
        }, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
            }
        });

    } catch (error) {
        console.error("Failed to get video URL:", error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : "Failed to get video URL" },
            { status: 500 }
        );
    }
}

export async function OPTIONS() {
    return NextResponse.json({}, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
    });
} 