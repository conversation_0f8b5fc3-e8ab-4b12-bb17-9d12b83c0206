import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams;
        const userId = searchParams.get('userId');

        if (!userId) {
            return NextResponse.json({ error: "User ID is required" }, { status: 400 });
        }

        // Get the user's stream status
        const stream = await db.stream.findFirst({
            where: { userId },
            select: {
                isLive: true,
                name: true,
                updatedAt: true,
            },
            orderBy: { createdAt: 'asc' }
        });

        return NextResponse.json({
            userId,
            isLive: stream?.isLive || false,
            streamName: stream?.name,
            lastUpdated: stream?.updatedAt?.toISOString() || new Date().toISOString(),
        });
    } catch (error) {
        console.error("Error fetching live status:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
} 