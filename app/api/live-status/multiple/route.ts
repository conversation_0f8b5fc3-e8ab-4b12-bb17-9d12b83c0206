import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function POST(request: NextRequest) {
    try {
        const { userIds } = await request.json();

        if (!Array.isArray(userIds) || userIds.length === 0) {
            return NextResponse.json({ error: "User IDs array is required" }, { status: 400 });
        }

        // Get streams for all requested users
        const streams = await db.stream.findMany({
            where: {
                userId: { in: userIds }
            },
            select: {
                userId: true,
                isLive: true,
            },
        });

        // Create a map of userId -> isLive status
        const liveStatuses: Record<string, boolean> = {};

        // Initialize all users as offline
        userIds.forEach(userId => {
            liveStatuses[userId] = false;
        });

        // Update with actual live statuses
        streams.forEach(stream => {
            liveStatuses[stream.userId] = stream.isLive;
        });

        return NextResponse.json(liveStatuses);
    } catch (error) {
        console.error("Error fetching multiple live statuses:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
} 