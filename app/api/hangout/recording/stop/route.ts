import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { EgressClient } from "livekit-server-sdk";

const egressClient = new EgressClient(
    process.env.LIVEKIT_API_URL!,
    process.env.LIVEKIT_API_KEY!,
    process.env.LIVEKIT_API_SECRET!
);

export async function POST(req: NextRequest) {
    try {
        const { egressId, sessionId } = await req.json();

        if (!egressId || !sessionId) {
            return NextResponse.json({ error: "Egress ID and session ID are required" }, { status: 400 });
        }

        // Get authenticated user
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get the hangout recording session
        const recordedSession = await db.hangoutRecordedSession.findUnique({
            where: { id: sessionId },
            include: { host: true }
        });

        if (!recordedSession) {
            return NextResponse.json({ error: "Hangout recording session not found" }, { status: 404 });
        }

        // Verify user is the host
        if (recordedSession.host.externalUserId !== userId) {
            return NextResponse.json({ error: "Unauthorized - not the host" }, { status: 403 });
        }

        // Stop the egress
        const egressInfo = await egressClient.stopEgress(egressId);

        console.log(`[HANGOUT_RECORDING_STOP_DEBUG] Egress status: ${egressInfo.status}`);
        console.log(`[HANGOUT_RECORDING_STOP_DEBUG] Egress info:`, {
            egressId: egressInfo.egressId,
            status: egressInfo.status,
            startedAt: egressInfo.startedAt,
            endedAt: egressInfo.endedAt,
            error: egressInfo.error
        });

        // Update the hangout recording database record - mark as PUBLISHED and set publishedAt
        const updatedSession = await db.hangoutRecordedSession.update({
            where: { id: sessionId },
            data: {
                recordingEndedAt: new Date(),
                processingStatus: 'PUBLISHED',
                isPublished: true,
                publishedAt: new Date(),
            },
        });

        console.log(`[HANGOUT_RECORDING_STOP_DEBUG] Updated session:`, {
            id: updatedSession.id,
            processingStatus: updatedSession.processingStatus,
            isPublished: updatedSession.isPublished,
            publishedAt: updatedSession.publishedAt
        });

        console.log(`[HANGOUT_RECORDING_STOP] Stopped recording for session ${sessionId}, egress ID: ${egressId}`);

        return NextResponse.json({
            success: true,
            egressId: egressInfo.egressId,
            sessionId: updatedSession.id,
            status: egressInfo.status,
            message: "Recording stopped successfully"
        });

    } catch (error) {
        console.error("Failed to stop hangout recording:", error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : "Failed to stop hangout recording" },
            { status: 500 }
        );
    }
}
