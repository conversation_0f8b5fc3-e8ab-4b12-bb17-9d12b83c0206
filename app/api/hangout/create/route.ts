import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { createHangoutRoom } from "@/lib/hangout-service";
import { db } from "@/lib/db";

export async function POST(req: NextRequest) {
    try {
        const { userId } = await auth();
        if (!userId) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { name } = await req.json();
        if (!name || typeof name !== 'string') {
            return NextResponse.json({ error: "Room name is required" }, { status: 400 });
        }

        // Get user information from database
        const user = await db.user.findUnique({ where: { externalUserId: userId } });
        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        // Create actual hangout room in database
        const room = await createHangoutRoom(user.id, name.trim());

        console.log(`[HANGOUT_CREATE] Room created for ${user.username}: ${name}`);

        return NextResponse.json({
            success: true,
            room: {
                id: room.id,
                name: room.name,
                hostUserId: room.hostUserId,
                host: room.host,
                participantCount: room.participantCount,
                maxParticipants: room.maxParticipants,
                isPublic: room.isPublic,
                createdAt: room.createdAt.toISOString(),
                participants: room.participants,
            },
        });

    } catch (error) {
        console.error("Failed to create hangout room:", error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : "Failed to create hangout room" },
            { status: 500 }
        );
    }
} 