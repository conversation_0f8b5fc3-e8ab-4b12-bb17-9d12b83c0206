import { use } from "react";
import { getRecordedSessionById } from "@/lib/recorded-session-service";
import Image from "next/image";
import Link from "next/link";
import { ArrowLeft, Clock, Users, Play } from "lucide-react";
import { Button } from "@/components/ui/button";
import { VideoPlayer } from "./_components/video-player";

interface WatchPageProps {
    params: Promise<{ sessionId: string }>;
}

const formatDuration = (seconds: number | null) => {
    if (!seconds) return "Unknown";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const formatDate = (date: Date | null) => {
    if (!date) return "Unknown";
    return new Date(date).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    });
};

export default function WatchPage({ params }: WatchPageProps) {
    const { sessionId } = use(params);
    const session = use(getRecordedSessionById(sessionId));

    if (!session) {
        return (
            <div className="flex items-center justify-center min-h-[60vh] p-8">
                <div className="text-center space-y-4">
                    <h1 className="text-2xl font-bold">Recording Not Found</h1>
                    <p className="text-muted-foreground">
                        This recorded session doesn&apos;t exist or has been removed.
                    </p>
                    <Link href="/">
                        <Button>
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Home
                        </Button>
                    </Link>
                </div>
            </div>
        );
    }

    // Debug logging
    console.log('[WATCH_PAGE_DEBUG] Session data:', {
        id: session.id,
        videoUrl: session.videoUrl,
        hlsUrl: session.hlsUrl,
        processingStatus: session.processingStatus
    });

    return (
        <div className="container mx-auto px-4 py-6">
            {/* Header */}
            <div className="flex items-center gap-4 mb-6">
                <Link href="/">
                    <Button variant="ghost" size="sm">
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back
                    </Button>
                </Link>
                <div className="flex items-center gap-2">
                    <Play className="w-5 h-5 text-primary" />
                    <span className="font-medium text-primary">RECORDED SESSION</span>
                </div>
            </div>

            {/* Video Player Area */}
            <div className="bg-black rounded-lg aspect-video mb-6">
                <VideoPlayer sessionId={sessionId} session={session} />
            </div>

            {/* Session Info */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Info */}
                <div className="lg:col-span-2 space-y-4">
                    <div>
                        <h1 className="text-2xl font-bold mb-2">{session.title}</h1>
                        {session.description && (
                            <p className="text-muted-foreground">{session.description}</p>
                        )}
                    </div>

                    <div className="flex items-center gap-6 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                            <Users className="w-4 h-4" />
                            <span>{session.participantCount} participant{session.participantCount === 1 ? "" : "s"}</span>
                        </div>
                        <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{formatDuration(session.duration)}</span>
                        </div>
                        <div>
                            Recorded {formatDate(session.recordingStartedAt)}
                        </div>
                    </div>

                    {/* Host Info */}
                    <div className="flex items-center gap-3 p-4 bg-muted rounded-lg">
                        <div className="w-12 h-12 bg-background rounded-full flex items-center justify-center overflow-hidden">
                            {session.host.imageURL ? (
                                <Image
                                    src={session.host.imageURL}
                                    alt={session.host.username}
                                    width={48}
                                    height={48}
                                    className="w-full h-full object-cover"
                                />
                            ) : (
                                <span className="text-lg font-medium">
                                    {session.host.username.charAt(0).toUpperCase()}
                                </span>
                            )}
                        </div>
                        <div>
                            <p className="font-medium">{session.host.username}</p>
                            <p className="text-sm text-muted-foreground">Host</p>
                        </div>
                        <div className="ml-auto">
                            <Link href={`/${session.host.username}`}>
                                <Button variant="outline" size="sm">
                                    View Profile
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Sidebar */}
                <div className="space-y-4">
                    <div className="bg-muted p-4 rounded-lg">
                        <h3 className="font-medium mb-2">Session Details</h3>
                        <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Status:</span>
                                <span className="font-medium text-green-600">Published</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Duration:</span>
                                <span>{formatDuration(session.duration)}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Participants:</span>
                                <span>{session.participantCount}</span>
                            </div>
                            {session.publishedAt && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Published:</span>
                                    <span>{formatDate(session.publishedAt)}</span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Debug Info for Development */}
                    {process.env.NODE_ENV === 'development' && (
                        <div className="bg-muted p-4 rounded-lg">
                            <h3 className="font-medium mb-2">Debug Info</h3>
                            <div className="space-y-1 text-xs text-muted-foreground">
                                <div>Session ID: {session.id}</div>
                                <div>Egress ID: {session.egressId}</div>
                                <div>Video URL: {session.videoUrl}</div>
                                <div>HLS URL: {session.hlsUrl}</div>
                                <div>Processing Status: {session.processingStatus}</div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
} 