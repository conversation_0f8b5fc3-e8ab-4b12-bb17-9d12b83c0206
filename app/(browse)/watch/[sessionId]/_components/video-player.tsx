"use client";

import { useState, useEffect, useCallback } from "react";
import { Play, Loader2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface VideoPlayerProps {
    sessionId: string;
    session: {
        id: string;
        title: string;
        videoUrl: string | null;
        hlsUrl: string | null;
        processingStatus: string;
        thumbnailUrl: string | null;
    };
}

interface VideoResponse {
    success: boolean;
    videoUrl: string;
    format: string;
    session: Record<string, unknown>;
    expiresAt: string;
    error?: string;
}

export const VideoPlayer = ({ sessionId, session }: VideoPlayerProps) => {
    const [videoUrl, setVideoUrl] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [format, setFormat] = useState<'mp4' | 'hls'>('mp4');

    const fetchVideoUrl = useCallback(async (requestedFormat: 'mp4' | 'hls' = 'mp4') => {
        setLoading(true);
        setError(null);

        try {
            console.log(`[VIDEO_PLAYER] Fetching ${requestedFormat} URL for session:`, sessionId);

            const response = await fetch(`/api/videos/${sessionId}?format=${requestedFormat}`);
            const data: VideoResponse = await response.json();

            console.log(`[VIDEO_PLAYER] API Response:`, data);

            if (data.success && data.videoUrl) {
                setVideoUrl(data.videoUrl);
                setFormat(requestedFormat);
                console.log(`[VIDEO_PLAYER] Successfully loaded ${requestedFormat} URL:`, data.videoUrl);
            } else {
                throw new Error(data.error || `Failed to load ${requestedFormat} video`);
            }
        } catch (err) {
            console.error(`[VIDEO_PLAYER] Error fetching ${requestedFormat} URL:`, err);
            setError(err instanceof Error ? err.message : `Failed to load ${requestedFormat} video`);
        } finally {
            setLoading(false);
        }
    }, [sessionId]);

    // Auto-load video on mount
    useEffect(() => {
        if (session.processingStatus === 'PUBLISHED' && (session.videoUrl || session.hlsUrl)) {
            // Try MP4 first, fallback to HLS if needed
            fetchVideoUrl('mp4');
        }
    }, [sessionId, session.processingStatus, session.videoUrl, session.hlsUrl, fetchVideoUrl]);

    const handleVideoError = () => {
        console.log('[VIDEO_PLAYER] MP4 failed, trying HLS...');
        if (format === 'mp4' && session.hlsUrl) {
            fetchVideoUrl('hls');
        } else {
            setError('Video failed to load. The file may not be ready yet.');
        }
    };

    // Show processing state
    if (session.processingStatus !== 'PUBLISHED') {
        return (
            <div className="w-full h-full flex items-center justify-center text-white">
                <div className="text-center">
                    <Loader2 className="w-16 h-16 mx-auto mb-4 animate-spin opacity-50" />
                    <p className="text-lg mb-2">Video Processing</p>
                    <p className="text-sm opacity-75">
                        This recording is being processed and will be available soon.
                    </p>
                    <p className="text-xs opacity-50 mt-2">
                        Status: {session.processingStatus}
                    </p>
                </div>
            </div>
        );
    }

    // Show loading state
    if (loading) {
        return (
            <div className="w-full h-full flex items-center justify-center text-white">
                <div className="text-center">
                    <Loader2 className="w-16 h-16 mx-auto mb-4 animate-spin" />
                    <p className="text-lg mb-2">Loading Video...</p>
                    <p className="text-sm opacity-75">
                        Fetching secure video URL ({format.toUpperCase()})
                    </p>
                </div>
            </div>
        );
    }

    // Show error state
    if (error && !videoUrl) {
        return (
            <div className="w-full h-full flex items-center justify-center text-white">
                <div className="text-center space-y-4">
                    <AlertCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p className="text-lg mb-2">Video Error</p>
                    <p className="text-sm opacity-75 max-w-md">
                        {error}
                    </p>
                    <Button
                        onClick={() => fetchVideoUrl('mp4')}
                        variant="outline"
                        size="sm"
                        className="text-white border-white/20 hover:bg-white/10"
                    >
                        <Play className="w-4 h-4 mr-2" />
                        Retry
                    </Button>
                </div>
            </div>
        );
    }

    // Show video player
    if (videoUrl) {
        return (
            <div className="w-full h-full">
                {format === 'hls' ? (
                    // HLS player (could be enhanced with HLS.js later)
                    <video
                        controls
                        className="w-full h-full rounded-lg"
                        poster={session.thumbnailUrl || undefined}
                        onError={handleVideoError}
                        crossOrigin="anonymous"
                    >
                        <source src={videoUrl} type="application/x-mpegURL" />
                        Your browser does not support HLS playback.
                    </video>
                ) : (
                    // MP4 player
                    <video
                        controls
                        className="w-full h-full rounded-lg"
                        poster={session.thumbnailUrl || undefined}
                        onError={handleVideoError}
                        crossOrigin="anonymous"
                    >
                        <source src={videoUrl} type="video/mp4" />
                        Your browser does not support the video tag.
                    </video>
                )}

                {/* Format switcher for debugging */}
                {process.env.NODE_ENV === 'development' && (
                    <div className="absolute top-2 right-2 space-x-2">
                        <Button
                            size="sm"
                            variant={format === 'mp4' ? 'default' : 'outline'}
                            onClick={() => fetchVideoUrl('mp4')}
                            disabled={loading || !session.videoUrl}
                        >
                            MP4
                        </Button>
                        <Button
                            size="sm"
                            variant={format === 'hls' ? 'default' : 'outline'}
                            onClick={() => fetchVideoUrl('hls')}
                            disabled={loading || !session.hlsUrl}
                        >
                            HLS
                        </Button>
                    </div>
                )}
            </div>
        );
    }

    // Fallback - show manual load button
    return (
        <div className="w-full h-full flex items-center justify-center text-white">
            <div className="text-center space-y-4">
                <Play className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg mb-2">Ready to Play</p>
                <p className="text-sm opacity-75">
                    Click to load the video with secure access
                </p>
                <Button
                    onClick={() => fetchVideoUrl('mp4')}
                    variant="outline"
                    size="lg"
                    className="text-white border-white/20 hover:bg-white/10"
                >
                    <Play className="w-4 h-4 mr-2" />
                    Load Video
                </Button>
            </div>
        </div>
    );
}; 