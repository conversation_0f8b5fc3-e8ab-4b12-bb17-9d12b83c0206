"use client";
import { useRef, useState, useEffect } from "react";

export default function Page() {
  const videoSrc = "/uploads/ios-compatible.mp4"; // ✅ iOS-friendly video used for mock feed
  const [videos, setVideos] = useState<number[]>([0, 1, 2, 3, 4]);
  const sentinelRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setVideos((prev) => [...prev, prev.length]);
        }
      },
      {
        rootMargin: "100px",
        threshold: 0.1,
      }
    );

    const current = sentinelRef.current;
    if (current) observer.observe(current);

    return () => {
      if (current) observer.unobserve(current);
    };
  }, []);

  return (
    <div
      className="fixed top-[60px] left-0 right-0 bottom-0 overflow-y-auto snap-y snap-mandatory hide-scrollbar bg-muted"
      style={{ WebkitOverflowScrolling: "touch" }}
    >
      {videos.map((id) => (
        <VideoSection key={id} src={videoSrc} />
      ))}
      <div ref={sentinelRef} className="h-1" />
    </div>
  );
}

function VideoSection({ src }: { src: string }) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const sectionRef = useRef<HTMLElement>(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isMuted, setIsMuted] = useState(true);
  const [autoplayFailed, setAutoplayFailed] = useState(false);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (video.paused) {
      video.play().catch(() => setAutoplayFailed(true));
      setIsPlaying(true);
    } else {
      video.pause();
      setIsPlaying(false);
    }
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !video.muted;
    setIsMuted(video.muted);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      async ([entry]) => {
        const video = videoRef.current;
        if (!video) return;

        if (entry.isIntersecting) {
          try {
            await video.play();
            setIsPlaying(true);
          } catch (err) {
            console.warn("Autoplay failed, showing tap-to-play", err);
            setAutoplayFailed(true);
          }
        } else {
          video.pause();
          setIsPlaying(false);
        }
      },
      { threshold: 0.6 }
    );

    const section = sectionRef.current;
    if (section) observer.observe(section);

    return () => {
      if (section) observer.unobserve(section);
    };
  }, []);

  return (
    <section
      ref={sectionRef}
      className="snap-start w-full relative bg-muted"
      style={{ height: "calc(100dvh - 60px)" }}
    >
      <video
        ref={videoRef}
        src={src}
        autoPlay
        loop
        muted={isMuted}
        playsInline
        preload="auto"
        className="w-full h-full object-contain"
      />

      {/* Only shown if autoplay fails */}
      {autoplayFailed && (
        <button
          onClick={togglePlay}
          className="absolute inset-0 z-10 flex items-center justify-center bg-black/60 text-white text-xl font-bold"
        >
          Tap to Play
        </button>
      )}

      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex gap-4 bg-black/50 px-4 py-2 rounded-full">
        <button
          onClick={togglePlay}
          className="text-white text-sm px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded"
        >
          {isPlaying ? "Pause" : "Play"}
        </button>
        <button
          onClick={toggleMute}
          className="text-white text-sm px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded"
        >
          {isMuted ? "Sound On" : "Mute"}
        </button>
      </div>
    </section>
  );
}
