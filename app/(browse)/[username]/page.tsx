// app/(browse)/[username]/page.tsx

import { use } from "react";
import { hasBlockedUser } from "@/lib/block-service";
import { isFollowingUser } from "@/lib/follow-service";
import { getUserByUsername } from "@/lib/user-service";
import { getRecordedSessionsByUser } from "@/lib/recorded-session-service";
import Image from "next/image";
import Link from "next/link";
import { Actions } from "./_components/actions";
import { Play, Clock, Users } from "lucide-react";
import { LiveStreamStatus } from "./_components/live-stream-status";
import { VerifiedBadge } from "../_components/VerifiedBadge";

export default function UserPage({
  params,
}: {
  params: Promise<{ username: string }>;
}) {
  const { username } = use(params);

  const user = use(getUserByUsername(username));
  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-xl font-semibold">User Not Found</p>
          <p className="text-muted-foreground mt-2">
            The user <strong>{username}</strong> does not exist.
          </p>
        </div>
      </div>
    );
  }

  const isFollowing = use(isFollowingUser(user.id));
  const hasBlocked = use(hasBlockedUser(user.id));
  const recordedSessions = use(getRecordedSessionsByUser(user.id));

  if (hasBlocked) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-base font-semibold">User is blocked</p>
          <p className="text-muted-foreground mt-2">
            You have blocked this user.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col mt-5 pl-8 sm:px-6 lg:px-8">
      <div className="flex items-center gap-3">
        {/* Profile Image */}
        <div className="w-24 h-24 rounded-full overflow-hidden">
          <Image
            src={user.imageURL}
            alt={`${username}'s profile`}
            width={96}
            height={96}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Username & Actions */}
        <div className="flex flex-col">
          <div className="flex items-center gap-1">
            <p className="text-base capitalize">{user.name}</p>
            <p className="text-base">({user.username})</p>
            {user.username && (
              <Link
                href={`https://instagram.com/${user.username}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  src="/instagram.png"
                  alt={`${user.username}'s Instagram`}
                  width={20}
                  height={20}
                  className="object-cover"
                />
              </Link>
            )}

            {user.isVerified && <VerifiedBadge />}
          </div>
          <p className="text-base">From {user.location}</p>
          <div className="mt-1">
            <Actions
              userId={user.id}
              isFollowing={isFollowing}
              username={username}
              isBlocked={hasBlocked}
            />
          </div>
        </div>
      </div>

      {/* Stream section */}
      <div className="mt-6">
        <LiveStreamStatus
          userId={user.id}
          username={username}
          streamName={user.streams?.[0]?.name}
          hasStream={!!user.streams?.[0]}
        />
      </div>

      {/* Recorded Sessions section */}
      {recordedSessions.length > 0 && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold text-white mb-4">
            Recent Recordings
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recordedSessions.slice(0, 6).map((session) => (
              <Link key={session.id} href={`/watch/${session.id}`}>
                <div className="bg-gray-900/50 rounded-lg border border-gray-800 hover:border-gray-700 transition-colors p-4">
                  <div className="aspect-video bg-gray-800 rounded-lg flex items-center justify-center mb-3 relative">
                    {session.thumbnailUrl ? (
                      <Image
                        src={session.thumbnailUrl}
                        alt={session.title}
                        fill
                        className="object-cover rounded-lg"
                      />
                    ) : (
                      <Play className="w-8 h-8 text-gray-400" />
                    )}
                    <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                      <Play className="w-8 h-8 text-white" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-semibold text-white text-sm line-clamp-2">
                      {session.title}
                    </h3>

                    <div className="flex items-center justify-between text-xs text-gray-400">
                      <div className="flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        <span>{session.participantCount}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>
                          {session.duration
                            ? `${Math.floor(session.duration / 60)}:${(
                                session.duration % 60
                              )
                                .toString()
                                .padStart(2, "0")}`
                            : "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {recordedSessions.length > 6 && (
            <div className="mt-4 text-center">
              <Link
                href={`/${username}/recordings`}
                className="text-blue-400 hover:text-blue-300 text-sm"
              >
                View all {recordedSessions.length} recordings →
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
