"use client";

import { onFollow, onUnfollow } from "@/actions/follow";
import { Button } from "@/components/ui/button";
import { useTransition } from "react";
import { toast } from "sonner";

interface ActionsProps {
  isFollowing: boolean;
  userId: string;
  username: string;
  isBlocked: boolean; // ✅ Add this
  style?: React.CSSProperties;
}

export const Actions = ({ isFollowing, userId, isBlocked }: ActionsProps) => {
  const [isPending, startTransition] = useTransition();

  const handleFollow = () => {
    if (isBlocked) {
      toast.error("You must unblock this user before following.");
      return;
    }

    startTransition(() => {
      onFollow(userId)
        .then((data) => {
          if (data && data.following) {
            toast.success(`You are now following ${data.following.username}`);
          } else {
            toast.error("Follow action failed.");
          }
        })
        .catch(() => toast.error("Something went wrong"));
    });
  };

  const handleUnfollow = () => {
    startTransition(() => {
      onUnfollow(userId)
        .then((data) => {
          if (data && data.following) {
            toast.success(`You have unfollowed ${data.following.username}`);
          } else {
            toast.error("Unfollow action failed.");
          }
        })
        .catch(() => toast.error("Something went wrong"));
    });
  };

  const onClick = () => {
    if (isBlocked) {
      toast.error("Unblock the user to follow them.");
      return;
    }

    if (isFollowing) {
      handleUnfollow();
    } else {
      handleFollow();
    }
  };

  return (
    <Button disabled={isPending} onClick={onClick} variant="primary" style={{ width: "90px", height: "30px" }}>
      {isBlocked ? "Unblock to follow" : isFollowing ? "Unfollow" : "Follow"}
    </Button>
  );
};
