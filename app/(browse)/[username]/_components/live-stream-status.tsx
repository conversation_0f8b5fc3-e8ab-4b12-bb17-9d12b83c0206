"use client";

import { useLiveStatus } from "@/hooks/use-live-status";
import Link from "next/link";

interface LiveStreamStatusProps {
    userId: string;
    username: string;
    streamName?: string;
    hasStream: boolean;
}

export function LiveStreamStatus({
    userId,
    username,
    streamName,
    hasStream
}: LiveStreamStatusProps) {
    const { isLive, isLoading } = useLiveStatus(); // Poll every 45 seconds

    if (!hasStream) {
        return (
            <div className="bg-gray-900/30 rounded-lg p-4 border border-gray-800">
                <p className="text-gray-400">
                    {username} hasn&apos;t set up streaming yet.
                </p>
            </div>
        );
    }

    return (
        <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-800">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                        {isLoading ? (
                            <>
                                <div className="w-3 h-3 bg-gray-400 rounded-full animate-pulse"></div>
                                <span className="text-gray-400 font-semibold text-sm">CHECKING...</span>
                            </>
                        ) : isLive ? (
                            <>
                                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                                <span className="text-red-500 font-semibold text-sm">LIVE</span>
                            </>
                        ) : (
                            <>
                                <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                                <span className="text-gray-500 font-semibold text-sm">OFFLINE</span>
                            </>
                        )}
                    </div>
                    <div>
                        <h3 className="font-semibold text-white">{streamName}</h3>
                        <p className="text-sm text-gray-400">
                            {isLoading
                                ? "Checking status..."
                                : isLive
                                    ? "Currently streaming"
                                    : "Stream available"
                            }
                        </p>
                    </div>
                </div>

                {isLive && !isLoading && (
                    <Link
                        href={`/rooms/${userId}`}
                        className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
                    >
                        Join Stream
                    </Link>
                )}
            </div>

            {!isLive && !isLoading && (
                <p className="text-sm text-gray-500 mt-2">
                    {username} is not currently streaming. Check back later!
                </p>
            )}
        </div>
    );
} 