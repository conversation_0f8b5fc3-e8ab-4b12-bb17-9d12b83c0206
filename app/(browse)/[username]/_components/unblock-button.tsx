"use client";

import { useTransition, useState } from "react";
import { onBlock, onUnblock } from "@/actions/block";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface ToggleBlockButtonProps {
  userId: string;
  username: string;
  isBlocked: boolean;
}

export const UnblockButton = ({
  userId,
  username,
  isBlocked: initialBlocked,
}: ToggleBlockButtonProps) => {
  const [isPending, startTransition] = useTransition();
  const [isBlocked, setIsBlocked] = useState(initialBlocked);
  const router = useRouter();

  const handleToggleBlock = () => {
    startTransition(() => {
      const action = isBlocked ? onUnblock : onBlock;

      action(userId)
        .then((data) => {
          if (data && data.blocked) {
            toast.success(
              `${isBlocked ? "Unblocked" : "Blocked"} ${data.blocked.username}`
            );
          } else {
            toast.error("Action failed.");
          }

          setIsBlocked(!isBlocked); // toggle UI
          router.refresh(); // update UI
        })
        .catch(() => toast.error("Something went wrong"));
    });
  };

  return (
    <Button disabled={isPending} onClick={handleToggleBlock}>
      {isBlocked ? "Unblock" : "Block"} {username}
    </Button>
  );
};
