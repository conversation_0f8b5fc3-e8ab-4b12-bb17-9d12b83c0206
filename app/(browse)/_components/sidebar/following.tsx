"use client";

import { useSidebar } from "@/store/use-sidebar";
import { UserItem, UserItemSkeleton } from "./user-item";

interface FollowingProps {
  data: Array<{
    id: string;
    followerId: string;
    followingId: string;
    createdAt: Date;
    updatedAt: Date;
    following: {
      id: string;
      username: string;
      imageURL: string;
      externalUserId: string;
      bio: string | null;
      name: string | null;
      birthday: Date | null;
      location: string | null;
      gender: string | null;
      interests: string[];
      profileComplete: boolean;
      isVerified: boolean;
      createdAt: Date;
      updatedAt: Date;
    };
  }>;
}

export const Following = ({ data }: FollowingProps) => {
  const { collapsed } = useSidebar((state) => state);
  if (data.length === 0) {
    return (
      <div className="text-muted-foreground text-sm">
        No users followed
      </div>
    );
  }

  return (
    <div>
      {!collapsed && (
        <div className="pl-6 mb-2">
          <p className="text-sm font-lg">Following</p>
        </div>
      )}
      <ul className="space-y-2 px-2">
        {data.map((follow) => (
          <UserItem
            key={follow.following.id}
            username={follow.following.username}
            imageUrl={follow.following.imageURL}
          />
        ))}
      </ul>
    </div>
  );
};

export const FollowingSkeleton = () => {
  return (
    <div>
      {[...Array(3)].map((_, i) => (
        <UserItemSkeleton key={i} />
      ))}
    </div>
  );
};
