"use client";

import Link from "next/link";
import { useSidebar } from "@/store/use-sidebar";
import { UserAvatar } from "@/components/user-avatar";
import { Skeleton } from "@/components/ui/skeleton";

interface UserItemProps {
  username: string;
  imageUrl: string;
  href?: string;
  customLabel?: string;
  customIcon?: React.ReactNode;
  participantCount?: number;
  isLive?: boolean;
}

export const UserItem = ({
  username,
  imageUrl,
  href,
  customLabel,
  customIcon,
  participantCount,
  isLive = false
}: UserItemProps) => {
  const { collapsed } = useSidebar((state) => state);

  const linkHref = href || `/${username}`;
  const displayLabel = customLabel || username;

  return (
    <div className="group flex items-center w-full p-2 rounded-md hover:bg-accent/5">
      <Link href={linkHref} className="flex items-center w-full gap-x-4">
        <div className="relative">
          <UserAvatar
            imageUrl={imageUrl}
            username={username}
            showBadge={isLive}
          />
          {customIcon && (
            <div className="absolute -bottom-1 -right-1 bg-background rounded-full p-1">
              {customIcon}
            </div>
          )}
        </div>
        {!collapsed && (
          <div className="flex-1 min-w-0">
            <p className="truncate text-sm font-medium">{displayLabel}</p>
            {participantCount !== undefined && (
              <p className="text-xs text-muted-foreground">
                {participantCount} {participantCount === 1 ? 'person' : 'people'}
              </p>
            )}
          </div>
        )}
      </Link>
    </div>
  );
};

export const UserItemSkeleton = () => {
  return (
    <div className="flex items-center gap-x-4 px-3 py-2">
      <Skeleton className="min-h-[32px] min-w-[32px] rounded-full" />
      <div className="flex-1">
        <Skeleton className="h-6" />
      </div>
    </div>
  );
};
