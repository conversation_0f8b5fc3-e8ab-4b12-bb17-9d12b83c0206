"use client";

import { useSidebar } from "@/store/use-sidebar";
import { Follow, User } from "@prisma/client";
import { UserItem, UserItemSkeleton } from "./user-item";

interface FollowingWithLiveStatusProps {
    data: (Follow & {
        following: User & { streams: { isLive: boolean }[] };
    })[];
}

export const FollowingWithLiveStatus = ({ data }: FollowingWithLiveStatusProps) => {
    const { collapsed } = useSidebar((state) => state);

    // Extract user IDs for live status polling
    // const { liveStatuses, isLoading } = useMultipleLiveStatus(); // Poll every 60 seconds

    if (!data || !data.length) {
        return null;
    }

    return (
        <div>
            {!collapsed && (
                <div className="pl-6 mb-2">
                    <p className="text-sm font-lg">Following</p>
                </div>
            )}
            <ul className="space-y-2 px-2">
                {data.map((follow) => {
                    // Use real-time live status if available, fallback to database value
                    const isLive = follow.following.streams?.[0]?.isLive ?? false;

                    return (
                        <UserItem
                            key={follow.following.id}
                            username={follow.following.username}
                            imageUrl={follow.following.imageURL}
                            isLive={isLive}
                        />
                    );
                })}
            </ul>
        </div>
    );
};

export const FollowingSkeleton = () => {
    return (
        <ul className="px-2 pt-2 lg:pt-0">
            {[...Array(3)].map((_, i) => (
                <UserItemSkeleton key={i} />
            ))}
        </ul>
    );
}; 