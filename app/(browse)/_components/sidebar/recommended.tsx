"use client";

import { useSidebar } from "@/store/use-sidebar";
import { User } from "@prisma/client";
import { UserItem, UserItemSkeleton } from "./user-item";

interface RecommendedProps {
  data: (User & { streams: { isLive: boolean }[] })[]; // ✅ Updated to use streams array
}

export const Recommended = ({ data }: RecommendedProps) => {
  const { collapsed } = useSidebar((state) => state);

  const showLabel = !collapsed && data && data.length > 0;

  return (
    <div>
      {showLabel && (
        <div className="pl-6 mb-2">
          <p className="text-sm ">Recommended</p>
        </div>
      )}
      <ul className="space-y-2 px-2">
        {data &&
          Array.isArray(data) &&
          data.map((user) => (
            <UserItem
              key={user.id}
              username={user.username}
              imageUrl={user.imageURL}
              isLive={user.streams?.[0]?.isLive}
            />
          ))}
      </ul>
    </div>
  );
};

export const RecommendedSkeleton = () => {
  return (
    <ul className="px-2">
      {[...Array(3)].map((_, i) => (
        <UserItemSkeleton key={i} />
      ))}
    </ul>
  );
};
