import { getRecommended } from "@/lib/recommended-service";
import { Recommended, RecommendedSkeleton } from "./recommended";
import { Toggle } from "./toggle";
import { Wrapper } from "./wrapper";
import { getFollowedUsers } from "@/lib/follow-service";
import { Following } from "./following";
import { FollowingSkeleton } from "./following";
import { getActiveHangoutRooms } from "@/lib/hangout-service";
import { ActiveHangouts, ActiveHangoutsSkeleton } from "./active-hangouts";

interface SidebarProps {
  recommendedUsers: Awaited<ReturnType<typeof getRecommended>>;
  followedUsers: Awaited<ReturnType<typeof getFollowedUsers>>;
  activeHangouts: Awaited<ReturnType<typeof getActiveHangoutRooms>>;
}

export const SidebarContent = ({
  recommendedUsers,
  followedUsers,
  activeHangouts,
}: SidebarProps) => {
  return (
    <Wrapper>
      <Toggle />

      <div className="space-y-4 pt-4 lg:pt-0">
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        <ActiveHangouts data={activeHangouts as any} />
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        <Following data={followedUsers as any} />
        <Recommended data={recommendedUsers} />
      </div>
    </Wrapper>
  );
};

export const Sidebar = async () => {
  const [recommendedUsers, followedUsers, activeHangouts] = await Promise.all([
    getRecommended(),
    getFollowedUsers(),
    getActiveHangoutRooms(),
  ]);

  return (
    <SidebarContent
      recommendedUsers={recommendedUsers}
      followedUsers={followedUsers}
      activeHangouts={activeHangouts}
    />
  );
};

export const SidebarSkeleton = () => {
  return (
    <aside className="fixed left-0 flex flex-col w-[70px] lg:w-[220px] h-full bg-[#2D2E35] border-r border-[#2D2E35] z-50">
      <ActiveHangoutsSkeleton />
      <FollowingSkeleton />
      <RecommendedSkeleton />
    </aside>
  );
};
