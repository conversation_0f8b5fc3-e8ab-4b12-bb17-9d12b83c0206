"use client";

import { useSidebar } from "@/store/use-sidebar";
import { User } from "@prisma/client";
import { UserItem } from "./user-item";
import { MessageCircle } from "lucide-react";

interface HangoutRoom {
  id: string;
  name: string;
  host: User;
  participantCount: number;
  maxParticipants: number;
  participants: Array<{
    user: User;
  }>;
}

interface ActiveHangoutsProps {
  data: HangoutRoom[];
}

export const ActiveHangouts = ({ data }: ActiveHangoutsProps) => {
  const { collapsed } = useSidebar((state) => state);

  const label = "Active Hangouts";

  if (!data?.length) {
    return null;
  }

  return (
    <div>
      {!collapsed && (
        <div className="pl-6 mb-4">
          <p className="text-sm text-muted-foreground">
            {label}
          </p>
        </div>
      )}
      <ul className="space-y-2 px-2">
        {data.map((hangout) => (
          <UserItem
            key={hangout.id}
            username={hangout.host.username}
            imageUrl={hangout.host.imageURL}
            isLive={true}
            href={`/hangouts/${hangout.id}`}
            customLabel={hangout.name}
            customIcon={<MessageCircle className="h-4 w-4" />}
            participantCount={hangout.participantCount}
          />
        ))}
      </ul>
    </div>
  );
};

export const ActiveHangoutsSkeleton = () => {
  return (
    <ul className="px-2 pt-2 lg:pt-0">
      {[...Array(3)].map((_, i) => (
        <li key={i} className="flex items-center gap-x-4 px-3 py-2">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-gray-700 rounded-full animate-pulse" />
          </div>
          <div className="flex-1 lg:block hidden">
            <div className="h-4 bg-gray-700 rounded animate-pulse mb-1" />
            <div className="h-3 bg-gray-700 rounded animate-pulse w-2/3" />
          </div>
        </li>
      ))}
    </ul>
  );
};
