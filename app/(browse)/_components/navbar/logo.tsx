import Link from "next/link";
import Image from "next/image";
// import { <PERSON><PERSON><PERSON>, <PERSON>sand } from "next/font/google";
// import { cn } from "@/lib/utils";

// const font = Quicksand({
//   subsets: ["latin"],
//   weight: ["300", "400", "500", "600", "700"],
// });

export const Logo = () => {
  return (
    <Link href="/">
      <div className="flex items-center gap-x-4 hover:opacity-75 transition">
        <div className="bg-transparent rounded-full p-1 mr-12 shrink-0 lg:mr-0 lg:shrink">
          <Image src="/popcircle.png" alt="Popcircle" height="32" width="32" />
        </div>

        {/* <div className={cn("hidden lg:block", font.className)}>
          <p className="text-base font-semibold">Popcircle</p>
          <p className="text-xs text-baseuted-foreground">Let&apos;s play</p>
        </div> */}
      </div>
    </Link>
  );
};
