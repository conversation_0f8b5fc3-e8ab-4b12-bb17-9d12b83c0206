"use client";

import { useUser } from "@clerk/nextjs";
import { UserButton } from "@clerk/nextjs";
import { But<PERSON> } from "@/components/ui/button";
import { Users, LayoutDashboard } from "lucide-react";
import Link from "next/link";

export const Actions = () => {
  const { user, isLoaded } = useUser();

  return (
    <div className="flex items-center justify-end gap-x-2 ml-4 lg:ml-0">
      {!isLoaded ? null : !user ? (
        <Link href="/sign-in">
          <Button size="sm" variant="primary">
            Login
          </Button>
        </Link>
      ) : (
        <div className="flex items-center gap-x-4">
          <Button
            size="sm"
            variant="ghost"
            className="text-muted-foreground hover:text-primary"
          >
            <Link href="/hangouts" className="flex items-center">
              <Users className="h-5 w-5 lg:mr-2" />
              <span className="hidden lg:block">Hangouts</span>
            </Link>
          </Button>

          <Button
            size="sm"
            variant="ghost"
            className="text-muted-foreground hover:text-primary"
          >
            <Link href={`/u/${user.username}`} className="flex items-center">
              <LayoutDashboard className="h-5 w-5 lg:mr-2" />
              <span className="hidden lg:block">Dashboard</span>
            </Link>
          </Button>

          <UserButton afterSignOutUrl="/" />
        </div>
      )}
    </div>
  );
};
