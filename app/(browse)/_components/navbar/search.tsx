"use client";

import { useState, useEffect, useRef } from "react";
import { SearchIcon, XIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import Image from "next/image";

export const Search = () => {
  const router = useRouter();
  const [value, setValue] = useState("");
  const [results, setResults] = useState<
    { username: string; imageURL: string }[]
  >([]);
  const [savedSearches, setSavedSearches] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  // Outside click handler
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(e.target as Node)
      ) {
        setIsFocused(false);
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Load saved searches
  useEffect(() => {
    const stored = localStorage.getItem("recentSearches");
    if (stored) setSavedSearches(JSON.parse(stored));
  }, []);

  const updateSavedSearches = (term: string) => {
    const prev = JSON.parse(localStorage.getItem("recentSearches") || "[]");
    const updated = [
      term,
      ...prev.filter((item: string) => item !== term),
    ].slice(0, 5);
    localStorage.setItem("recentSearches", JSON.stringify(updated));
    setSavedSearches(updated);
  };

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!value.trim()) return;
    updateSavedSearches(value.trim());
    router.push(`/search?term=${encodeURIComponent(value.trim())}`);
    setShowDropdown(false);
  };

  const onClear = () => {
    setValue("");
    setResults([]);
    setShowDropdown(false);
    inputRef.current?.focus();
  };

  useEffect(() => {
    if (!value) {
      setResults([]);
      setShowDropdown(isFocused);
      return;
    }

    const delayDebounce = setTimeout(async () => {
      setLoading(true);
      try {
        const res = await fetch(`/api/search-users?term=${value}`);
        const data = await res.json();
        setResults(data || []);
      } catch {
        setResults([]);
      }
      setLoading(false);
      setShowDropdown(true);
    }, 300);

    return () => clearTimeout(delayDebounce);
  }, [value, isFocused]);

  return (
    <div ref={wrapperRef} className="relative w-full max-w-md">
      <form onSubmit={onSubmit}>
        <div className="relative flex items-center w-full max-w-[95%] sm:max-w-[400px] mx-auto bg-muted  hover:bg-muted focus-within:bg-muted transition-colors rounded-full  px-3 sm:px-4 py-2 overflow-hidden shrink">
          <input
            ref={inputRef}
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onFocus={() => {
              setIsFocused(true);
              setShowDropdown(true);
            }}
            placeholder=""
            className="flex-1 bg-transparent text-white placeholder-muted-foreground border-none focus:outline-none sm:text-base pr-10"
          />

          {value && (
            <XIcon
              className="absolute right-10 sm:right-12 h-5 w-5 text-white cursor-pointer hover:bg-gray-300 transition flex-shrink-0 mr-3"
              onClick={onClear}
            />
          )}
          <button
            type="submit"
            className="absolute right-2 sm:right-4 p-2 bg-muted hover:bg-muted/80 rounded-full transition flex items-center flex-shrink-0"
          >
            <SearchIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>
      </form>

      {showDropdown && (value || savedSearches.length > 0) && (
        <div className="absolute top-full left-0 w-full bg-[#1a1a1a] mt-2 rounded-lg shadow-md max-h-64 overflow-auto z-50">
          {loading ? (
            <div className="px-4 py-2 text-sm text-muted-foreground">
              Loading...
            </div>
          ) : value && results.length > 0 ? (
            results.map((user, index) => (
              <div
                key={index}
                className="px-4 py-2 flex items-center gap-3 text-sm text-white hover:bg-muted cursor-pointer"
                onClick={() => {
                  updateSavedSearches(user.username);
                  router.push(`/${user.username}`);
                  setShowDropdown(false);
                }}
              >
                <Image
                  src={user.imageURL || "/default-avatar.png"}
                  alt={user.username}
                  width={24}
                  height={24}
                  className="rounded-full object-cover"
                />
                <span>{user.username}</span>
              </div>
            ))
          ) : value ? (
            <div
              className="px-4 py-2 text-sm text-muted-foreground hover:bg-muted cursor-pointer"
              onClick={() => {
                updateSavedSearches(value.trim());
                router.push(`/search?term=${encodeURIComponent(value.trim())}`);
                setShowDropdown(false);
              }}
            >
              Search for &ldquo;{value}&rdquo;
            </div>
          ) : savedSearches.length > 0 ? (
            <>
              {savedSearches.map((term, index) => (
                <div
                  key={index}
                  className="px-4 py-2 flex justify-between items-center text-sm text-muted-foreground hover:bg-muted"
                >
                  <button
                    type="button"
                    onClick={() => {
                      setValue(term);
                      updateSavedSearches(term);
                      setShowDropdown(false);
                      router.push(`/${term}`);
                    }}
                    className="flex-1 text-left"
                  >
                    {term}
                  </button>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const updated = savedSearches.filter((t) => t !== term);
                      localStorage.setItem(
                        "recentSearches",
                        JSON.stringify(updated)
                      );
                      setSavedSearches(updated);
                    }}
                    className="ml-2 text-xs text-gray-400 hover:text-white"
                  >
                    ✕
                  </button>
                </div>
              ))}
              <div className="border-t border-muted mt-1">
                <button
                  onClick={() => {
                    localStorage.removeItem("recentSearches");
                    setSavedSearches([]);
                  }}
                  className="w-full text-left px-4 py-2 text-xs text-gray-400 hover:text-white"
                >
                  Clear recent searches
                </button>
              </div>
            </>
          ) : null}
        </div>
      )}
    </div>
  );
};
