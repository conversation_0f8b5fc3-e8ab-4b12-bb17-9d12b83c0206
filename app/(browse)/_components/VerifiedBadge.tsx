// components/VerifiedBadge.tsx
"use client";

import { useState } from "react";
import Image from "next/image";

export const VerifiedBadge = () => {
  const [showLabel, setShowLabel] = useState(false);

  return (
    <div className="relative flex items-center gap-2">
      <button
        onClick={() => setShowLabel((prev) => !prev)}
        className="p-1 rounded hover:bg-muted"
      >
        <Image
          src="/verified.png"
          alt="Verified user icon"
          width={20}
          height={20}
          className="object-cover"
        />
      </button>

      {showLabel && (
        <span className="text-xs text-gray-300 bg-black px-2 py-1 rounded">
          This account is verified.
        </span>
      )}
    </div>
  );
};
