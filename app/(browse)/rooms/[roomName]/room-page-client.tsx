'use client';

import React, { useState, useCallback } from 'react';
import { ConnectionDetails } from '@/lib/client-utils';
import { PreJoin } from '@/components/stream-player/pre-join';
import { VideoConferenceComponent } from '@/components/stream-player/video-conference-component';
import { LocalUserChoices } from '@livekit/components-react';
import { VideoCodec } from 'livekit-client';
import { User, Stream } from '@prisma/client';
import { useUser } from '@clerk/nextjs';
import { toast } from 'sonner';

interface RoomPageClientProps {
  roomName: string;
  region?: string;
  hq: boolean;
  codec: VideoCodec;
  hostUser: User;
  stream: Stream;
}

export const RoomPageClient: React.FC<RoomPageClientProps> = ({
  roomName,
  region,
  hq,
  codec,
  hostUser,
  stream
}) => {
  const { user } = useUser();
  const [connectionDetails, setConnectionDetails] = useState<ConnectionDetails | undefined>();
  const [preJoinChoices, setPreJoinChoices] = useState<LocalUserChoices | undefined>();

  const handlePreJoinSubmit = useCallback(async (choices: LocalUserChoices) => {
    try {
      const url = new URL('/api/connection-details', window.location.origin);
      url.searchParams.set('roomName', roomName);
      url.searchParams.set('participantName', choices.username);
      
      if (region) {
        url.searchParams.set('region', region);
      }

      const response = await fetch(url.toString());
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get connection details: ${errorText}`);
      }

      const connectionDetails: ConnectionDetails = await response.json();
      setConnectionDetails(connectionDetails);
      setPreJoinChoices(choices);
    } catch (error) {
      console.error('Error getting connection details:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to join room');
    }
  }, [roomName, region]);

  const handlePreJoinError = useCallback((error: Error) => {
    console.error('PreJoin error:', error);
    toast.error(error.message);
  }, []);

  // Default participant name based on user
  const defaultParticipantName = user?.firstName || user?.username || '';

  const preJoinDefaults = {
    username: defaultParticipantName,
    videoEnabled: true,
    audioEnabled: true,
  };

  return (
    <div className="h-full" data-lk-theme="default">
      {connectionDetails === undefined || preJoinChoices === undefined ? (
        <PreJoin
          defaults={preJoinDefaults}
          onSubmit={handlePreJoinSubmit}
          onError={handlePreJoinError}
          participantName={defaultParticipantName}
          roomName={hostUser.username}
        />
      ) : (
        <VideoConferenceComponent
          connectionDetails={connectionDetails}
          userChoices={preJoinChoices}
          options={{ codec, hq }}
          hostUser={hostUser}
          stream={stream}
        />
      )}
    </div>
  );
};
