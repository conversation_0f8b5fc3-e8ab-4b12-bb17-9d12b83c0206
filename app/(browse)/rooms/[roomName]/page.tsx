import * as React from "react";
import { RoomPageClient } from "./room-page-client";
import { isVideoCodec } from "@/lib/client-utils";
import { getUserById } from "@/lib/user-service";
import { notFound } from "next/navigation";

export default async function RoomPage({
  params,
  searchParams,
}: {
  params: Promise<{ roomName: string }>;
  searchParams: Promise<{
    region?: string;
    hq?: string;
    codec?: string;
  }>;
}) {
  const _params = await params;
  const _searchParams = await searchParams;

  // Validate codec
  const codec =
    typeof _searchParams.codec === "string" && isVideoCodec(_searchParams.codec)
      ? _searchParams.codec
      : "vp9";
  const hq = _searchParams.hq === "true" ? true : false;

  // Get host user information
  const hostUser = await getUserById(_params.roomName);
  if (!hostUser) {
    notFound();
  }

  // Get the user's stream
  const stream = hostUser.streams?.[0];
  if (!stream) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Stream Not Found</h2>
          <p className="text-baseuted-foreground">
            {hostUser.username} is not currently streaming.
          </p>
        </div>
      </div>
    );
  }

  return (
    <RoomPageClient
      roomName={_params.roomName}
      region={_searchParams.region}
      hq={hq}
      codec={codec}
      hostUser={hostUser}
      stream={stream}
    />
  );
}
