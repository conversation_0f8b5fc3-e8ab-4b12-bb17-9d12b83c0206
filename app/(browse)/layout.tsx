import { Suspense } from "react";
import { Container } from "./_components/container";
import { Navbar } from "./_components/navbar";
import { Sidebar, SidebarSkeleton } from "./_components/sidebar";
import { ProfileCompletionWrapper } from "@/components/profile-completion-wrapper";

const BrowseLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <ProfileCompletionWrapper requiresAuth={false}>
      <Navbar />
      <div className="flex h-full pt-20">
        <aside className="hidden sm:block border-r border-gray-800">
          <Suspense fallback={<SidebarSkeleton />}>
            <Sidebar />
          </Suspense>
        </aside>
        <Container>{children}</Container>
      </div>
    </ProfileCompletionWrapper>
  );
};
export default BrowseLayout;
