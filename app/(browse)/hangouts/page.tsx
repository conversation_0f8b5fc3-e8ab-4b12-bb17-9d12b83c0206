import { HangoutLanding } from "@/components/hangout-landing";
import { currentUser } from "@clerk/nextjs/server";
import { getActiveHangoutRooms } from "@/lib/hangout-service";
import { db } from "@/lib/db";

export default async function HangoutsPage() {
    const external = await currentUser();
    let current = null;
    if (external) {
        current = await db.user.findUnique({ where: { externalUserId: external.id } });
    }

    const activeHangouts = await getActiveHangoutRooms();

    // Transform the data to match the expected interface
    const transformedHangouts = activeHangouts.map(hangout => ({
        ...hangout,
        createdAt: hangout.createdAt.toISOString(),
    }));

    return (
        <HangoutLanding
            activeHangouts={transformedHangouts}
            currentUser={current}
        />
    );
} 