// app/search/page.tsx
"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState, Suspense } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

function SearchResults() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const term = searchParams.get("term") || "";
  const [results, setResults] = useState<
    { username: string; imageURL: string }[]
  >([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!term) return;

    const fetchResults = async () => {
      setLoading(true);
      try {
        const res = await fetch(`/api/search-users?term=${term}`);
        const data = await res.json();
        setResults(data || []);
      } catch (error) {
        console.error("Error fetching search results:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchResults();
  }, [term]);

  return (
    <div className="max-w-xl mx-auto px-4 py-6">
      <h1 className="text-2xl font-semibold mb-4 text-white">
        Search Results for &ldquo;{term}&rdquo;
      </h1>

      {loading ? (
        <p className="text-muted-foreground">Loading...</p>
      ) : results.length === 0 ? (
        <p className="text-muted-foreground">No users found.</p>
      ) : (
        <ul className="space-y-4">
          {results.map((user) => (
            <li
              key={user.username}
              className="flex items-center gap-4 cursor-pointer hover:bg-muted p-2 rounded"
              onClick={() => router.push(`/${user.username}`)}
            >
              <Image
                src={user.imageURL || "/default-avatar.png"}
                alt={user.username}
                width={40}
                height={40}
                className="rounded-full object-cover"
              />
              <span className="text-white">{user.username}</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={<div className="max-w-xl mx-auto px-4 py-6 text-white">Loading search...</div>}>
      <SearchResults />
    </Suspense>
  );
}
