'use client';

import { useEffect, useState } from 'react';
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Room<PERSON>udio<PERSON><PERSON><PERSON>,
    useParticipants,
    useLocalParticipant,
    VideoTrack,
    useRemoteParticipants,
    useRoomContext
} from '@livekit/components-react';
import { Track, Participant, Room } from 'livekit-client';
import { activeSpeakerObserver } from '@livekit/components-core';

// Custom hook for active speaker
function useActiveSpeaker(room: Room | undefined): Participant | undefined {
    const [activeSpeaker, setActiveSpeaker] = useState<Participant | undefined>(undefined);

    useEffect(() => {
        if (!room) return;

        const subscription = activeSpeakerObserver(room).subscribe((speakers) => {
            setActiveSpeaker(speakers[0] || undefined);
        });

        return () => subscription.unsubscribe();
    }, [room]);

    return activeSpeaker;
}

// Custom styles for 9:16 vertical recording
const RECORDING_STYLES = `
  .recording-container {
    width: 720px;
    height: 1280px;
    background: #000;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
  }
  
  .host-section {
    height: 50%;
    width: 100%;
    position: relative;
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .active-speaker-section {
    height: 50%;
    width: 100%;
    position: relative;
    background: #2a2a2a;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .video-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
  }
  
  .video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
  
  .participant-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .no-video-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 24px;
    font-weight: 600;
  }
`;

interface RecordingTemplateProps {
    token: string;
    url: string;
    layout?: string;
}

function VideoSection({
    participant,
    title,
    showName = true
}: {
    participant: Participant | null;
    title: string;
    showName?: boolean;
}) {
    const videoTrack = participant?.videoTrackPublications.values().next().value?.track;

    return (
        <div className="video-container">
            {participant && videoTrack ? (
                <>
                    <VideoTrack
                        trackRef={{
                            participant,
                            publication: participant.videoTrackPublications.values().next().value!,
                            source: Track.Source.Camera
                        }}
                    />
                    {showName && (
                        <div className="participant-info">
                            {participant.name || participant.identity}
                        </div>
                    )}
                </>
            ) : (
                <div className="no-video-placeholder">
                    {participant ? `${participant.name || participant.identity}` : title}
                </div>
            )}
        </div>
    );
}

function RecordingLayout() {
    const participants = useParticipants();
    const remoteParticipants = useRemoteParticipants();
    const room = useRoomContext();
    const activeSpeaker = useActiveSpeaker(room);
    const { localParticipant } = useLocalParticipant();

    // Find the host (first participant or local participant)
    const host = localParticipant || participants[0] || null;

    // Find the active speaker that's not the host
    const activeNonHostSpeaker = activeSpeaker && activeSpeaker.identity !== host?.identity ? activeSpeaker : null;

    // If no active speaker or active speaker is host, show the first remote participant
    const bottomParticipant = activeNonHostSpeaker ||
        (remoteParticipants.find(p => p.identity !== host?.identity) || remoteParticipants[0]) ||
        null;

    return (
        <div className="recording-container">
            {/* Host section - top 50% */}
            <div className="host-section">
                <VideoSection
                    participant={host}
                    title="Host"
                    showName={true}
                />
            </div>

            {/* Active speaker section - bottom 50% */}
            <div className="active-speaker-section">
                <VideoSection
                    participant={bottomParticipant}
                    title="Speaker"
                    showName={true}
                />
            </div>
        </div>
    );
}

function RecordingTemplate({ token, url }: RecordingTemplateProps) {
    const [isRecording, setIsRecording] = useState(false);

    useEffect(() => {
        // Signal that we're ready to start recording
        const startRecording = () => {
            console.log('START_RECORDING');
            setIsRecording(true);
        };

        // Start recording after a short delay to ensure everything is loaded
        const timer = setTimeout(startRecording, 2000);

        return () => {
            clearTimeout(timer);
            if (isRecording) {
                console.log('END_RECORDING');
            }
        };
    }, [isRecording]);

    // Handle room disconnect to end recording
    useEffect(() => {
        const handleBeforeUnload = () => {
            if (isRecording) {
                console.log('END_RECORDING');
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);
        return () => window.removeEventListener('beforeunload', handleBeforeUnload);
    }, [isRecording]);

    return (
        <>
            <style dangerouslySetInnerHTML={{ __html: RECORDING_STYLES }} />
            <LiveKitRoom
                token={token}
                serverUrl={url}
                connect={true}
                audio={true}
                video={true}
                onDisconnected={() => {
                    if (isRecording) {
                        console.log('END_RECORDING');
                    }
                }}
            >
                <RecordingLayout />
                <RoomAudioRenderer />
            </LiveKitRoom>
        </>
    );
}

export default function RecordingTemplatePage() {
    const [templateProps, setTemplateProps] = useState<RecordingTemplateProps | null>(null);

    useEffect(() => {
        // Parse URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const url = urlParams.get('url');
        const layout = urlParams.get('layout') || 'vertical-split';

        if (token && url) {
            setTemplateProps({ token, url, layout });
        } else {
            console.error('Missing required parameters: token and url');
        }
    }, []);

    if (!templateProps) {
        return (
            <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100vh',
                background: '#000',
                color: '#fff',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
            }}>
                Loading recording template...
            </div>
        );
    }

    return <RecordingTemplate {...templateProps} />;
} 