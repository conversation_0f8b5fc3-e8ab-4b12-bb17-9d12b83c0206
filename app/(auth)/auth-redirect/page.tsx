import { currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { redirect } from "next/navigation";

export default async function AuthRedirectPage() {
  const user = await currentUser();

  if (!user) {
    redirect("/sign-in");
  }

  const dbUser = await db.user.findUnique({
    where: { externalUserId: user.id },
  });

  if (!dbUser || !dbUser.profileComplete) {
    redirect("/complete-profile");
  }

  redirect(`/`); // Or wherever you want logged-in users to land
}
