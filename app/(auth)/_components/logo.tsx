import Image from "next/image";
// import { Quicksand } from "next/font/google";
// import { cn } from "@/lib/utils";

// const font = Poppins({
//   subsets: ["latin"],
//   weight: ["200", "300", "400", "500", "600", "700", "800"],
// });
// const font = Quicksand({
//   subsets: ["latin"],
//   weight: ["300", "400", "500", "600", "700"],
// });

export const Logo = () => {
  return (
    <div className="flex flex-col items-center gap-y-4">
      <div className="bg-transparent rounded-full p-1">
        <Image src="/popcircle.svg" alt="Popcircle" height="80" width="80" />
      </div>
      {/* <div className={cn("flex flex-col items-center", font.className)}>
        <p className="text-xl font-semibold">Popcircle</p>
        <p className="text-basem text-baseuted-foreground">Let&apos;s play</p>
      </div> */}
    </div>
  );
};
