import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { dark } from "@clerk/themes";
import type { Metadata } from "next";
import { Toaster } from "sonner";
import { Source_Code_Pro } from "next/font/google";
import "./globals.css";
import "@livekit/components-styles";
import "@livekit/components-styles/prefabs";

import { ThemeProvider } from "@/components/theme-provider";

const sourceCodePro = Source_Code_Pro({
  subsets: ["latin"],
  weight: ["200", "300", "400", "500", "600", "700", "800", "900"],
  style: ["normal", "italic"],
  variable: "--font-source-code-pro",
});

export const metadata: Metadata = {
  title: "Popcircle - A place to hangout.",
  description:
    "Connect, stream, and engage with PopCircle's modern video platform",
  keywords: ["streaming", "video", "live", "chat", "community"],
  authors: [{ name: "PopCircle Team" }],
  openGraph: {
    title: "PopCircle - Video Streaming Platform",
    description:
      "Connect, stream, and engage with PopCircle's modern video platform",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider
      signInFallbackRedirectUrl="/auth-redirect"
      signUpFallbackRedirectUrl="/auth-redirect"
      appearance={{
        baseTheme: dark,
        variables: {
          colorPrimary: "hsl(26 89% 65%)", // Converted from oklch(0.6083 0.2090 27.0276)
          colorBackground: "hsl(0 0% 13%)", // Converted from oklch(0.2178 0 0)
          colorText: "hsl(0 0% 91%)", // Converted from oklch(0.9067 0 0)
          fontFamily: "SansSerif, sans-serif",
          borderRadius: "0px", // Angular design from popcircle theme
        },
        elements: {
          formButtonPrimary: {
            backgroundColor: "hsl(26 89% 65%)", // Converted from oklch(0.6083 0.2090 27.0276)
            color: "hsl(0 0% 100%)", // Converted from oklch(1.0000 0 0)
            fontFamily: "SansSerif, sans-serif",
            fontWeight: "500",
            borderRadius: "0px", // Angular design from popcircle theme
            "&:hover": {
              backgroundColor: "hsl(246 49% 84%)", // Converted from oklch(0.7482 0.1235 244.7492)
            },
          },
          card: {
            backgroundColor: "hsl(0 0% 18%)", // Converted from oklch(0.2850 0 0)
            border: "1px solid hsl(0 0% 25%)", // Converted from oklch(0.4091 0 0)
            borderRadius: "0px", // Angular design from popcircle theme
            boxShadow:
              "0px 2px 5px 0px hsl(0 0% 0% / 0.60), 0px 1px 2px -1px hsl(0 0% 0% / 0.60)",
          },
          headerTitle: {
            fontFamily: "SansSerif, sans-serif",
            fontWeight: "600",
          },
          headerSubtitle: {
            fontFamily: "SansSerif, sans-serif",
          },
        },
      }}
    >
      <html lang="en" suppressHydrationWarning>
        <body
          className={`font-sans antialiased ${sourceCodePro?.variable || ""}`}
        >
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            enableSystem={true}
            disableTransitionOnChange={false}
          >
            <div className="min-h-screen bg-background text-foreground">
              {children}
            </div>
            <Toaster
              theme="dark"
              position="bottom-right"
              toastOptions={{
                style: {
                  background: "var(--card)",
                  color: "var(--card-foreground)",
                  border: "1px solid var(--border)",
                  borderRadius: "var(--radius)",
                  fontFamily: "var(--font-sans)",
                  boxShadow: "var(--shadow-lg)",
                },
              }}
            />
          </ThemeProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
