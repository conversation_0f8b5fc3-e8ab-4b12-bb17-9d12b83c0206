import { <PERSON><PERSON> } from "@/components/ui/button";
import { User<PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { LogOut } from "lucide-react";
import Link from "next/link";

export const Actions = () => {
  return (
    <div className="flex items-center justify-end gap-x-2">
      <Button
        size="sm"
        variant="ghost"
        className="text-baseuted-foreground hover:text-primary"
        asChild
      >
        <Link href="/">
          <LogOut className="h-5 w-5 mr-1" />
          Exit
        </Link>
      </Button>
      <UserButton afterSignOutUrl="/" />
    </div>
  );
};
