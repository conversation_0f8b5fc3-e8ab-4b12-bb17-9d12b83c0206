import { getSelf } from "@/lib/auth-service";
import { getStreamByUserId } from "@/lib/stream-service";
import { BlockedUsers } from "./_components/blocked-users";

const CommunityPage = async () => {
  const self = await getSelf();
  if (!self) {
    throw new Error("Unauthorized");
  }

  const stream = await getStreamByUserId(self.id);
  if (!stream) {
    throw new Error("Stream not found");
  }

  return (
    <div className="p-6">
      <div className="mb-4">
        <h1 className="text-2xl font-bold">Community Settings</h1>
        <p className="text-baseuted-foreground">
          Manage your community and blocked users
        </p>
      </div>
      <div className="space-y-4">
        <BlockedUsers />
      </div>
    </div>
  );
};

export default CommunityPage;
