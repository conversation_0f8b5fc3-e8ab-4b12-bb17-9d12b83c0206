"use client";

import { useTransition } from "react";
import { onUnblock } from "@/actions/block";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface UnblockButtonProps {
  userId: string;
}

export const UnblockButton = ({
  userId,
}: UnblockButtonProps) => {
  const [isPending, startTransition] = useTransition();

  const handleUnblock = () => {
    startTransition(() => {
      onUnblock(userId)
        .then((data) => {
          if (data && data.blocked) {
            toast.success(`Unblocked ${data.blocked.username}`);
          } else {
            toast.error("Unblock action failed.");
          }
        })
        .catch(() => toast.error("Something went wrong"));
    });
  };

  return (
    <Button
      disabled={isPending}
      onClick={handleUnblock}
      variant="outline"
      size="sm"
    >
      {isPending ? "Unblocking..." : "Unblock"}
    </Button>
  );
};
