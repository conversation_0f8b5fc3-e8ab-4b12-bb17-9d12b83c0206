import { getBlockedUsers } from "@/lib/block-service";
import { UnblockButton } from "./unblock-button";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

type BlockedUserWithDetails = {
  id: string;
  blockerId: string;
  blockedId: string;
  blocked?: {
    id: string;
    username: string;
    imageURL: string;
  };
};

export const BlockedUsers = async () => {
  const blockedUsers = (await getBlockedUsers()) as BlockedUserWithDetails[];

  if (blockedUsers.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Blocked Users</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-baseuted-foreground text-center py-4">
            No blocked users found.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Blocked Users ({blockedUsers.length})</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {blockedUsers.map((blockedUser) => (
            <div
              key={blockedUser.id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={blockedUser.blocked?.imageURL} />
                  <AvatarFallback>
                    {(blockedUser.blocked?.username || "U")
                      .charAt(0)
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">
                    {blockedUser.blocked?.username || "Unknown User"}
                  </p>
                  <p className="text-basem text-baseuted-foreground">
                    Blocked user
                  </p>
                </div>
              </div>
              <UnblockButton userId={blockedUser.blocked?.id || ""} />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export const BlockedUsersSkeleton = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Blocked Users</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <Skeleton className="h-9 w-20" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
