import { getSelfByUsername } from "@/lib/auth-service";
import { redirect } from "next/navigation";
import { Navbar } from "./_components/navbar";
import { Sidebar } from "./_components/sidebar";
import { Container } from "./_components/container";
import { ProfileCompletionWrapper } from "@/components/profile-completion-wrapper";

interface CreatorLayoutProps {
  params: Promise<{ username: string }>;
  children: React.ReactNode;
}

const CreatorLayout = async ({ params, children }: CreatorLayoutProps) => {
  const { username } = await params;
  const self = await getSelfByUsername(username);
  if (!self) {
    redirect("/");
  }
  return (
    <ProfileCompletionWrapper requiresAuth={true}>
      <Navbar />
      <div className="flex h-full pt-20">
        <Sidebar />
        <Container>{children}</Container>
      </div>
    </ProfileCompletionWrapper>
  );
};

export default CreatorLayout;
