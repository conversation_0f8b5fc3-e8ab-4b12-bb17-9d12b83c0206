import { StreamPlayer } from "@/components/stream-player";
import { getUserByUsername } from "@/lib/user-service";
import { currentUser } from "@clerk/nextjs/server";

interface CreatorPageProps {
  params: Promise<{ username: string }>;
}

const CreatorPage = async ({ params }: CreatorPageProps) => {
  const { username } = await params;
  const externalUser = await currentUser();
  const user = await getUserByUsername(username);

  if (!user || user.externalUserId !== externalUser?.id) {
    throw new Error("Unauthorized");
  }

  // Transform user to have stream property for StreamPlayer compatibility
  const userWithStream = {
    ...user,
    stream: user.streams?.[0] || null,
  };

  return (
    <div className="h-full">
      <StreamPlayer
        user={userWithStream}
        stream={userWithStream.stream}
        isFollowing
      />
    </div>
  );
};

export default CreatorPage;
