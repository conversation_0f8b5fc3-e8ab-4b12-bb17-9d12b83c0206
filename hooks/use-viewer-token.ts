import { useEffect, useState } from "react";
import { toast } from "sonner";
import { JwtPayload, jwtDecode } from "jwt-decode";
import { StreamRole } from "@prisma/client";

interface TokenMetadata {
  role?: StreamRole;
  permissions?: {
    canPublish: boolean;
    canPublishData: boolean;
    canPublishScreen: boolean;
    canSubscribe: boolean;
    canUpdateMetadata: boolean;
  };
}

export const useViewerToken = (hostUsername: string) => {
  const [token, setToken] = useState("");
  const [name, setName] = useState("");
  const [identity, setIdentity] = useState("");
  const [role, setRole] = useState<StreamRole>(StreamRole.VIEWER);
  const [permissions, setPermissions] =
    useState<TokenMetadata["permissions"]>();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const createToken = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log("Creating token for host:", hostUsername);

        const response = await fetch("/api/token", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ hostUsername }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to create token");
        }

        const data = await response.json();
        console.log("Token API response:", data);

        setToken(data.token);
        setName(data.name);
        setIdentity(data.identity);

        // Decode token to get metadata
        const decodedToken = jwtDecode(data.token) as JwtPayload & {
          name?: string;
          metadata?: string;
        };

        console.log("Decoded token:", {
          name: decodedToken.name,
          identity: decodedToken.sub,
          metadata: decodedToken.metadata,
        });

        // Parse metadata for role and permissions
        if (decodedToken.metadata) {
          try {
            const metadata: TokenMetadata = JSON.parse(decodedToken.metadata);
            if (metadata.role) {
              setRole(metadata.role);
            }
            if (metadata.permissions) {
              setPermissions(metadata.permissions);
            }
          } catch (e) {
            console.warn("Failed to parse token metadata:", e);
          }
        }
      } catch (error) {
        console.error("Token creation failed:", error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to create viewer token";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (hostUsername) {
      createToken();
    }
  }, [hostUsername]);

  return { token, name, identity, role, permissions, isLoading, error };
};
