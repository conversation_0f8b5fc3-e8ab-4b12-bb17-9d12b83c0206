import { useEffect, useState } from "react";
import { toast } from "sonner";
import { JwtPayload, jwtDecode } from "jwt-decode";
import { StreamRole } from "@prisma/client";

interface TokenMetadata {
  role?: StreamRole;
  permissions?: {
    canPublish: boolean;
    canPublishData: boolean;
    canPublishScreen: boolean;
    canSubscribe: boolean;
    canUpdateMetadata: boolean;
  };
}

export const useHangoutToken = (hangoutId: string) => {
  const [token, setToken] = useState("");
  const [name, setName] = useState("");
  const [identity, setIdentity] = useState("");
  const [role, setRole] = useState<StreamRole>(StreamRole.VIEWER);
  const [permissions, setPermissions] =
    useState<TokenMetadata["permissions"]>();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const createToken = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log("Creating hangout token for:", hangoutId);

        const response = await fetch("/api/hangout/token", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ hangoutId }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to create hangout token");
        }

        const data = await response.json();
        console.log("Hangout token API response:", data);

        setToken(data.token);
        setName(data.name);
        setIdentity(data.identity);

        // Decode token to get metadata
        const decodedToken = jwtDecode(data.token) as JwtPayload & {
          name?: string;
          metadata?: string;
        };

        console.log("Decoded hangout token:", {
          name: decodedToken.name,
          identity: decodedToken.sub,
          metadata: decodedToken.metadata,
        });

        // Parse metadata for role and permissions
        if (decodedToken.metadata) {
          try {
            const metadata: TokenMetadata = JSON.parse(decodedToken.metadata);
            if (metadata.role) {
              setRole(metadata.role);
            }
            if (metadata.permissions) {
              setPermissions(metadata.permissions);
            }
          } catch (e) {
            console.warn("Failed to parse hangout token metadata:", e);
          }
        }
      } catch (error) {
        console.error("Hangout token creation failed:", error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to create hangout token";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (hangoutId) {
      createToken();
    }
  }, [hangoutId]);

  return { token, name, identity, role, permissions, isLoading, error };
};
