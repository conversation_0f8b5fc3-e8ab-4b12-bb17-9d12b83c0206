"use client";

import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface UserProfile {
    id: string;
    username: string;
    profileComplete: boolean;
    externalUserId: string;
}

export function useProfileCompletion() {
    const { user, isLoaded } = useUser();
    const router = useRouter();
    const [dbUser, setDbUser] = useState<UserProfile | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (!isLoaded || !user) {
            setIsLoading(false);
            return;
        }

        const checkUserProfile = async () => {
            try {
                // Check if user exists in database
                const response = await fetch(`/api/user/profile?userId=${user.id}`);

                if (response.ok) {
                    const userData = await response.json();
                    setDbUser(userData);

                    // If user exists but profile is not complete, redirect to complete-profile
                    if (!userData.profileComplete && window.location.pathname !== '/complete-profile') {
                        router.push('/complete-profile');
                    }
                } else if (response.status === 404) {
                    // User doesn't exist in database, redirect to complete-profile
                    router.push('/complete-profile');
                }
            } catch (error) {
                console.error('Error checking user profile:', error);
            } finally {
                setIsLoading(false);
            }
        };

        checkUserProfile();
    }, [user, isLoaded, router]);

    return {
        user,
        dbUser,
        isLoading: isLoading || !isLoaded,
        isProfileComplete: dbUser?.profileComplete ?? false,
    };
} 