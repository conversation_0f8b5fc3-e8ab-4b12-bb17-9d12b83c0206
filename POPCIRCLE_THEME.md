# PopCircle Theme Implementation

## Overview

The PopCircle Theme is a comprehensive design system powered by **TweakCN** (doom64 theme) and implemented across the entire application, including all LiveKit components. It features a modern, angular color palette based on OKLCH color space, custom typography with SansSerif and Source Code Pro fonts, and cohesive styling throughout the platform.

## TweakCN Integration

PopCircle theme is built using TweakCN's doom64 theme, providing:

- **Angular design** with zero border radius (`--radius: 0px`)
- **Enhanced shadow system** with multiple shadow levels
- **Comprehensive theming** including sidebar and chart colors
- **Tailwind CSS v4 compatibility** with `@theme inline` support
- **Professional gaming aesthetic** with sharp, modern styling

### Installation Command

```bash
npx shadcn@latest add https://tweakcn.com/r/themes/doom-64.json
```

## Theme Features

### 🎨 Color Palette

**Light Mode:**

- Primary: `oklch(0.5016 0.1887 27.4816)` - Warm orange-red
- Secondary: `oklch(0.4955 0.0896 126.1858)` - Muted green
- Accent: `oklch(0.5880 0.0993 245.7394)` - Purple-blue
- Background: `oklch(0.8452 0 0)` - Light neutral
- Foreground: `oklch(0.2393 0 0)` - Dark text

**Dark Mode:**

- Primary: `oklch(0.6083 0.2090 27.0276)` - Brighter orange-red
- Secondary: `oklch(0.6423 0.1467 133.0145)` - Enhanced green
- Accent: `oklch(0.7482 0.1235 244.7492)` - Bright purple-blue
- Background: `oklch(0.2178 0 0)` - Dark neutral
- Foreground: `oklch(0.9067 0 0)` - Light text

### 🔤 Typography

**Primary Font:** SansSerif (sans-serif)

- Geometric, modern typeface
- Used for headings, UI text, and general content
- Weights: 200-800

**Monospace Font:** Source Code Pro

- Professional coding font
- Used for code blocks and technical content
- Includes italic variants

### 🎯 Design Principles

1. **Angular Design**: Zero border radius for sharp, gaming-inspired aesthetics
2. **Accessibility**: WCAG compliant color contrasts across light/dark modes
3. **Consistency**: Unified color variables across all components via TweakCN
4. **Responsiveness**: Mobile-first design with breakpoint considerations
5. **Performance**: Optimized font loading and CSS delivery

## Implementation Details

### Core Files

1. **`app/globals.css`** - Complete theme implementation with TweakCN doom64 theme
2. **`tailwind.config.js`** - Tailwind configuration with theme integration
3. **`app/layout.tsx`** - Font loading and theme provider setup
4. **`components.json`** - shadcn/ui configuration for TweakCN compatibility

### Theme Structure

```css
:root {
  /* Core colors */
  --background: oklch(0.8452 0 0);
  --foreground: oklch(0.2393 0 0);
  --primary: oklch(0.5016 0.1887 27.4816);

  /* Typography */
  --font-sans: "SansSerif", sans-serif;
  --font-mono: "Source Code Pro", monospace;

  /* Layout - Angular design */
  --radius: 0px;
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.4);

  /* TweakCN enhancements */
  --sidebar: oklch(0.7572 0 0);
  --chart-1: oklch(0.5016 0.1887 27.4816);
  --letter-spacing: 0em;
  --spacing: 0.25rem;
}

/* Tailwind CSS v4 compatibility */
@theme inline {
  --color-background: var(--background);
  --color-primary: var(--primary);
  --radius: 0px;
  /* ... additional theme tokens */
}
```

## LiveKit Integration

### Custom CSS Variables

All LiveKit components now use PopCircle theme variables:

```css
[data-lk-theme="default"] {
  --lk-bg: var(--background);
  --lk-fg: var(--foreground);
  --lk-accent: var(--primary);
  --lk-button-bg: var(--primary);
  --lk-font-family: var(--font-sans);
}
```

### Styled Components

- **Video Conference Layout**: Themed background and typography
- **Participant Tiles**: PopCircle borders, shadows, and hover effects
- **Control Bar**: Glass-morphism effect with PopCircle colors
- **Chat Interface**: Consistent with app design system
- **Buttons**: Hover animations and PopCircle color scheme
- **Settings Menus**: Unified popover styling

## Component Updates

### Updated Components

1. **LiveBadge**: Now uses `bg-destructive` instead of `bg-rose-500`
2. **Navbar Components**: Replaced hard-coded colors with theme variables
3. **VideoConferenceWrapper**: Updated to use PopCircle background and borders
4. **UI Components**: All shadcn/ui components use theme variables

### Theme-Aware Classes

```css
/* Custom utility classes */
.popcircle-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
}

.popcircle-text-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
```

## Usage Guidelines

### Using Theme Colors

```tsx
// Correct - Use theme variables
<div className="bg-primary text-primary-foreground">Content</div>

// Avoid - Hard-coded colors
<div className="bg-blue-500 text-white">Content</div>
```

### Typography

```tsx
// Headings automatically use SansSerif
<h1 className="font-semibold">PopCircle Heading</h1>

// Code blocks use Source Code Pro
<code className="font-mono">const theme = 'popcircle';</code>
```

### Shadows and Effects

```tsx
// Use theme shadows
<div className="shadow-lg">Card with PopCircle shadow</div>

// Use theme radius
<div className="rounded-lg">PopCircle border radius</div>
```

## Dark Mode Support

The theme automatically adapts to dark mode using CSS custom properties:

```tsx
// Theme provider setup in layout.tsx
<ThemeProvider
  attribute="class"
  defaultTheme="dark"
  enableSystem={true}
  disableTransitionOnChange={false}
>
```

## Browser Support

- **Modern browsers**: Full OKLCH support
- **Legacy browsers**: Graceful fallbacks included
- **Font loading**: Progressive enhancement with system fonts as fallbacks

## Performance Optimizations

1. **Font preloading**: Google Fonts preconnected in layout
2. **CSS layers**: Organized for optimal specificity
3. **Variable cascading**: Efficient color inheritance
4. **Selective imports**: Only necessary LiveKit styles loaded

## Future Enhancements

1. **Animation system**: Motion design tokens
2. **Component variants**: Extended theme variants
3. **Color scales**: Programmatic color generation
4. **Design tokens**: JSON-based token system

## Troubleshooting

### Common Issues

1. **Colors not updating**: Check CSS custom property inheritance
2. **Fonts not loading**: Verify Google Fonts connection
3. **LiveKit styling**: Ensure `data-lk-theme="default"` attribute is present

### Development Tools

Use browser DevTools to inspect CSS custom properties:

```css
/* Check computed values */
:root {
  --primary: oklch(0.5016 0.1887 27.4816);
}

/* Verify inheritance */
.lk-button {
  background: var(--lk-button-bg); /* Should resolve to primary */
}
```

## Contributing

When adding new components:

1. Use theme variables instead of hard-coded colors
2. Follow the established naming conventions
3. Test in both light and dark modes
4. Ensure LiveKit components inherit theme styling
5. Update documentation for new theme tokens

---

_PopCircle Theme v1.0 - Implemented with ❤️ for a cohesive user experience_
